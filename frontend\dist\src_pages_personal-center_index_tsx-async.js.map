{"version": 3, "sources": ["src/components/InvitationStatus.tsx", "src/pages/personal-center/DataOverview.tsx", "src/pages/personal-center/PersonalInfo.tsx", "src/pages/personal-center/TeamListCard.tsx", "src/pages/personal-center/TodoManagement.tsx", "src/pages/personal-center/UnifiedSettingsModal.tsx", "F:\\Project\\teamAuth\\frontend\\src\\pages\\personal-center\\UserInfoPopover.module.css?asmodule", "src/pages/personal-center/UserInfoPopover.tsx", "src/pages/personal-center/components/TeamManagementModal.tsx", "src/pages/personal-center/index.tsx", "src/services/todo.ts", "src/utils/teamSelectionUtils.ts"], "sourcesContent": ["/**\n * 邀请状态显示组件\n */\n\nimport React from 'react';\nimport { Tag } from 'antd';\nimport {\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  ExclamationCircleOutlined,\n  StopOutlined,\n} from '@ant-design/icons';\nimport { InvitationStatus } from '@/types/api';\n\ninterface InvitationStatusProps {\n  status: InvitationStatus;\n  isExpired?: boolean;\n}\n\n/**\n * 邀请状态组件\n */\nconst InvitationStatusComponent: React.FC<InvitationStatusProps> = ({ \n  status, \n  isExpired = false \n}) => {\n  // 如果已过期，优先显示过期状态\n  if (isExpired && status === InvitationStatus.PENDING) {\n    return (\n      <Tag icon={<ExclamationCircleOutlined />} color=\"orange\">\n        已过期\n      </Tag>\n    );\n  }\n\n  switch (status) {\n    case InvitationStatus.PENDING:\n      return (\n        <Tag icon={<ClockCircleOutlined />} color=\"blue\">\n          待确认\n        </Tag>\n      );\n    case InvitationStatus.ACCEPTED:\n      return (\n        <Tag icon={<CheckCircleOutlined />} color=\"green\">\n          已接受\n        </Tag>\n      );\n    case InvitationStatus.REJECTED:\n      return (\n        <Tag icon={<CloseCircleOutlined />} color=\"red\">\n          已拒绝\n        </Tag>\n      );\n    case InvitationStatus.EXPIRED:\n      return (\n        <Tag icon={<ExclamationCircleOutlined />} color=\"orange\">\n          已过期\n        </Tag>\n      );\n    case InvitationStatus.CANCELLED:\n      return (\n        <Tag icon={<StopOutlined />} color=\"default\">\n          已取消\n        </Tag>\n      );\n    default:\n      return (\n        <Tag color=\"default\">\n          未知状态\n        </Tag>\n      );\n  }\n};\n\nexport default InvitationStatusComponent;\n", "import {\n  BarChartOutlined,\n  CarOutlined,\n  UsergroupAddOutlined,\n  ExclamationCircleOutlined,\n  AlertOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Card,\n  Col,\n  Row,\n  Spin,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserPersonalStatsResponse } from '@/types/api';\n\n/**\n * 数据概览卡片组件\n *\n * 显示用户的个人统计数据，采用单行四列的水平布局。\n * 包括车辆、人员、预警、告警等指标的统计卡片。\n *\n * 主要功能：\n * 1. 显示车辆数量统计\n * 2. 显示人员数量统计\n * 3. 显示预警数量统计\n * 4. 显示告警数量统计\n *\n * 数据来源：\n * - 个人统计数据：通过UserService.getUserPersonalStats()获取\n *\n * 布局特点：\n * - 单行四列水平排列\n * - 每个统计项独立的卡片设计\n * - 响应式布局适配不同屏幕\n */\nconst DataOverview: React.FC = () => {\n  // 定义内联样式对象\n  const cardStyles = {\n    base: {\n      borderRadius: '8px',\n      border: '1px solid #d9d9d9',\n      height: '120px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      backgroundColor: 'transparent', // 移除背景色\n    },\n    vehicle: {\n      // backgroundColor: '#1890ff', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    personnel: {\n      // backgroundColor: '#52c41a', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    warning: {\n      // backgroundColor: '#faad14', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n    alert: {\n      // backgroundColor: '#f5222d', // 移除背景色\n      // color: 'white', // 移除白色文字\n    },\n  };\n  /**\n   * 个人统计数据状态管理\n   */\n  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({\n    vehicles: 0,\n    personnel: 0,\n    warnings: 0,\n    alerts: 0,\n  });\n\n  const [statsLoading, setStatsLoading] = useState(true);\n  const [statsError, setStatsError] = useState<string | null>(null);\n\n  // 获取统计数据\n  useEffect(() => {\n    const fetchStatsData = async () => {\n      try {\n        const stats = await UserService.getUserPersonalStats();\n        setPersonalStats(stats);\n        setStatsError(null);\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        setStatsError('获取统计数据失败，请稍后重试');\n      } finally {\n        setStatsLoading(false);\n      }\n    };\n\n    fetchStatsData();\n  }, []);\n\n  return (\n    <ProCard\n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />\n          <span>数据概览</span>\n        </div>\n      }\n      style={{\n        marginBottom: 16,\n        borderRadius: 8,\n        border: '1px solid #d9d9d9',\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '20px',\n      }}\n    >\n      {statsError ? (\n        <Alert\n          message=\"数据概览加载失败\"\n          description={statsError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 8,\n          }}\n        />\n      ) : (\n        <Spin spinning={statsLoading}>\n          {/* 单行四列布局 */}\n          <Row gutter={[16, 16]}>\n            {/* 车辆统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.vehicle,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <CarOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#1890ff',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#1890ff',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.vehicles}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#1890ff',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  车辆\n                </div>\n              </Card>\n            </Col>\n\n            {/* 人员统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.personnel,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <UsergroupAddOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#52c41a',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#52c41a',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.personnel}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#52c41a',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  人员\n                </div>\n              </Card>\n            </Col>\n\n            {/* 预警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.warning,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <ExclamationCircleOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#faad14',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#faad14',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.warnings}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#faad14',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  预警\n                </div>\n              </Card>\n            </Col>\n\n            {/* 告警统计 */}\n            <Col xs={12} sm={6} md={6} lg={6} xl={6}>\n              <Card\n                style={{\n                  ...cardStyles.base,\n                  ...cardStyles.alert,\n                }}\n                styles={{\n                  body: {\n                    padding: '20px 16px',\n                    textAlign: 'center',\n                  },\n                }}\n\n              >\n                <div style={{ marginBottom: 12 }}>\n                  <AlertOutlined\n                    style={{\n                      fontSize: 24,\n                      color: '#ff4d4f',\n                      marginBottom: 8,\n                    }}\n                  />\n                </div>\n                <div\n                  style={{\n                    fontSize: 32,\n                    fontWeight: 700,\n                    color: '#ff4d4f',\n                    lineHeight: 1,\n                    marginBottom: 8,\n                  }}\n                >\n                  {personalStats.alerts}\n                </div>\n                <div\n                  style={{\n                    fontSize: 14,\n                    color: '#ff4d4f',\n                    fontWeight: 600,\n                    opacity: 0.9, // 提高透明度以确保可读性\n                  }}\n                >\n                  告警\n                </div>\n              </Card>\n            </Col>\n          </Row>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default DataOverview;\n", "import {\n  QuestionCircleOutlined,\n  SettingOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Space,\n  Spin,\n  Typography,\n} from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\n\nimport React, { useEffect, useState } from 'react';\nimport { UserService } from '@/services/user';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport UnifiedSettingsModal from './UnifiedSettingsModal';\nimport UserInfoPopover from './UserInfoPopover';\n\nconst { Title, Text } = Typography;\n\n/**\n * 个人信息组件\n *\n * 显示用户的基本个人信息，采用简洁的卡片设计。\n * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。\n *\n * 主要功能：\n * 1. 显示用户头像（使用姓名首字母）\n * 2. 显示用户姓名（支持气泡卡片显示详细信息）\n * 3. 显示最后登录时间和登录团队\n * 4. 提供设置入口\n *\n * 数据来源：\n * - 用户详细信息：通过UserService.getUserProfileDetail()获取\n */\nconst PersonalInfo: React.FC = () => {\n  /**\n   * 用户详细信息状态管理\n   */\n  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({\n    name: '',\n    position: '',\n    email: '',\n    telephone: '',\n    registerDate: '',\n    lastLoginTime: '',\n    lastLoginTeam: '',\n    teamCount: 0,\n    avatar: '',\n  });\n\n  const [userInfoLoading, setUserInfoLoading] = useState(true);\n  const [userInfoError, setUserInfoError] = useState<string | null>(null);\n\n  // Modal状态管理\n  const [settingsModalVisible, setSettingsModalVisible] = useState(false);\n\n  // 获取用户数据\n  useEffect(() => {\n    const fetchUserData = async () => {\n      try {\n        const userDetail = await UserService.getUserProfileDetail();\n        setUserInfo(userDetail);\n        setUserInfoError(null);\n      } catch (error) {\n        console.error('获取用户详细信息失败:', error);\n        setUserInfoError('获取用户详细信息失败，请稍后重试');\n      } finally {\n        setUserInfoLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, []);\n\n  return (\n    <>\n      <ProCard\n        extra={\n          <Space size={16}>\n            <UserInfoPopover userInfo={userInfo}>\n              <QuestionCircleOutlined\n                style={{\n                  fontSize: 18,\n                  color: '#8c8c8c',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  padding: '4px',\n                  borderRadius: '50%',\n                  background: 'transparent',\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#1890ff';\n                  e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';\n                  e.currentTarget.style.transform = 'scale(1.1)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#8c8c8c';\n                  e.currentTarget.style.background = 'transparent';\n                  e.currentTarget.style.transform = 'scale(1)';\n                }}\n              />\n            </UserInfoPopover>\n            <SettingOutlined\n              style={{\n                fontSize: 18,\n                color: '#8c8c8c',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                padding: '4px',\n                borderRadius: '50%',\n                background: 'transparent',\n              }}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.color = '#1890ff';\n                e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';\n                e.currentTarget.style.transform = 'scale(1.1)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.color = '#8c8c8c';\n                e.currentTarget.style.background = 'transparent';\n                e.currentTarget.style.transform = 'scale(1)';\n              }}\n              onClick={() => setSettingsModalVisible(true)}\n            />\n          </Space>\n        }\n        style={{\n          marginBottom: 16,\n          borderRadius: 8,\n          border: '1px solid #d9d9d9',\n        }}\n      >\n      {userInfoError ? (\n        <Alert\n          message=\"个人信息加载失败\"\n          description={userInfoError}\n          type=\"error\"\n          showIcon\n          style={{\n            borderRadius: 12,\n            border: 'none',\n          }}\n        />\n      ) : (\n        <Spin spinning={userInfoLoading}>\n          {/* 问候区域 */}\n          <div style={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            padding: '16px 0'\n          }}>\n            {/* 问候语 */}\n            <Typography.Title\n              level={3}\n              style={{\n                margin: 0,\n                fontSize: 20,\n                color: '#262626',\n              }}\n            >\n              您好，{userInfo.name || '加载中...'}\n            </Typography.Title>\n          </div>\n        </Spin>\n      )}\n      </ProCard>\n\n      {/* 统一设置Modal */}\n      <UnifiedSettingsModal\n        visible={settingsModalVisible}\n        onCancel={() => setSettingsModalVisible(false)}\n        userInfo={userInfo}\n        onSuccess={() => {\n          // 可以在这里刷新用户信息\n          console.log('设置操作成功');\n        }}\n      />\n    </>\n  );\n};\n\nexport default PersonalInfo;\n", "import {\n  CarOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined,\n  LogoutOutlined,\n  MinusCircleOutlined,\n  RightOutlined,\n  SearchOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { history, useModel } from '@umijs/max';\nimport {\n  Alert,\n  Button,\n  Col,\n  Flex,\n  Input,\n  message,\n  Modal,\n  Row,\n  Spin,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ProCard, ProList } from '@ant-design/pro-components';\nimport React, { useCallback, useEffect, useMemo, useState } from 'react';\nimport { AuthService } from '@/services';\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\nimport {\n  getTeamIdFromCurrentToken,\n  hasTeamInCurrentToken,\n  getUserIdFromCurrentToken,\n} from '@/utils/tokenUtils';\nimport { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';\nimport TeamManagementModal from './components/TeamManagementModal';\n\nconst { Text, Title } = Typography;\n \n\n\n\n// 响应式布局样式\nconst styles = `\n  .team-item .ant-pro-card-body {\n    padding: 0 !important;\n  }\n\n  .team-item {\n    margin-bottom: 12px !important; /* 减少团队项目间距 */\n  }\n\n\n\n  @media (max-width: 768px) {\n    .team-item {\n      margin-bottom: 10px !important; /* 中等屏幕减少团队项目间距 */\n    }\n\n    .team-stats-row {\n      margin-top: 6px; /* 减少内部间距 */\n    }\n\n    .team-info-wrap {\n      gap: 6px !important; /* 减少内部间距 */\n    }\n  }\n\n  @media (max-width: 576px) {\n    .team-item {\n      margin-bottom: 8px !important; /* 小屏幕减少团队项目间距 */\n    }\n\n    .team-stats-row {\n      margin-top: 8px; /* 减少内部间距 */\n    }\n\n    .team-stats-col {\n      margin-bottom: 4px; /* 减少内部间距 */\n    }\n\n    .team-info-wrap {\n      gap: 6px !important; /* 减少内部间距 */\n    }\n\n    .team-meta-info {\n      flex-wrap: wrap;\n      gap: 6px !important; /* 减少内部间距 */\n    }\n\n    .team-status-badges {\n      flex-wrap: wrap;\n      gap: 4px !important; /* 减少内部间距 */\n      margin-top: 4px; /* 减少内部间距 */\n    }\n  }\n\n  @media (max-width: 480px) {\n    .team-item {\n      margin-bottom: 6px !important; /* 最小屏幕减少团队项目间距 */\n    }\n\n    .team-name-text {\n      font-size: 16px !important; /* 保持字体大小 */\n    }\n\n    .team-meta-text {\n      font-size: 13px !important; /* 保持字体大小 */\n    }\n\n    .team-meta-info {\n      gap: 4px !important; /* 减少内部间距 */\n    }\n\n    .team-status-badges {\n      gap: 3px !important; /* 减少内部间距 */\n    }\n  }\n`;\n\n/**\n * 团队列表卡片组件\n *\n * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，\n * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。\n *\n * 主要功能：\n * 1. 显示用户所属的所有团队\n * 2. 支持团队切换功能\n * 3. 支持创建新团队\n * 4. 显示当前选择的团队状态\n * 5. 处理团队切换过程中的状态管理\n *\n * 状态管理：\n * - 团队列表数据的获取和显示\n * - 团队切换过程的加载状态\n * - 创建团队模态框的状态\n * - 错误状态的处理和显示\n *\n * 团队切换逻辑：\n * 1. 检查用户登录状态\n * 2. 判断是否为当前团队（避免重复切换）\n * 3. 调用后端API进行团队切换\n * 4. 更新本地Token和全局状态\n * 5. 跳转到团队仪表盘\n *\n * 与全局状态的集成：\n * - 监听用户登录状态变化\n * - 同步团队切换后的状态更新\n * - 处理用户注销时的状态清理\n */\nconst TeamListCard: React.FC = () => {\n  /**\n   * 团队列表相关状态管理\n   *\n   * 这些状态用于管理团队列表的显示和交互：\n   * - teams: 用户所属的团队列表数据\n   * - loading: 团队列表加载状态\n   * - error: 错误信息（如网络错误、权限错误等）\n   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）\n   */\n  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);\n\n  /**\n   * 搜索功能相关状态管理\n   *\n   * 这些状态用于管理团队搜索功能：\n   * - searchText: 用户输入的搜索文本\n   * - debouncedSearchText: 去抖动后的搜索文本，用于实际过滤\n   */\n  const [searchText, setSearchText] = useState<string>('');\n  const [debouncedSearchText, setDebouncedSearchText] = useState<string>('');\n\n  // 模态框状态管理\n  const [teamManagementModalVisible, setTeamManagementModalVisible] = useState(false);\n  const [leaveTeamModalVisible, setLeaveTeamModalVisible] = useState(false);\n  const [selectedTeam, setSelectedTeam] = useState<TeamDetailResponse | null>(null);\n\n  /**\n   * 创建团队功能已移至设置页面\n   *\n   * 为了更好的用户体验和功能组织，创建团队功能已经移动到\n   * 专门的设置页面中。用户可以通过\"团队设置\"按钮跳转到\n   * 设置页面进行团队创建和管理操作。\n   */\n\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户和团队信息：\n   * - initialState: 包含当前用户和团队信息的全局状态\n   * - setInitialState: 更新全局状态的函数\n   * - currentTeam: 当前选择的团队信息\n   */\n  const { initialState, setInitialState } = useModel('@@initialState');\n  const currentTeam = initialState?.currentTeam;\n\n  /**\n   * Token信息提取\n   *\n   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：\n   * - currentTokenTeamId: Token中包含的团队ID\n   * - currentUserId: Token中包含的用户ID\n   * - hasTeamInToken: Token是否包含团队信息\n   *\n   * 这些信息用于：\n   * - 判断当前是否已选择团队\n   * - 确定哪个团队是当前激活的团队\n   * - 记录用户的团队选择历史\n   */\n  const currentTokenTeamId = getTeamIdFromCurrentToken();\n  const currentUserId = getUserIdFromCurrentToken();\n  const hasTeamInToken = hasTeamInCurrentToken();\n\n  // 判断是否有真正的当前团队：\n  // 1. Token中有团队信息（说明用户已经选择过团队）\n  // 2. initialState中有团队信息（说明已经获取过团队详情）\n  // 3. 两者的团队ID一致（确保状态同步）\n  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）\n  const hasRealCurrentTeam = !!(\n    hasTeamInToken &&\n    currentTokenTeamId &&\n    currentTeam &&\n    currentTeam.id === currentTokenTeamId &&\n    currentUserId &&\n    hasUserSelectedTeam(currentUserId, currentTokenTeamId)\n  );\n\n  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID\n  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;\n\n  // 调试日志\n  console.log('TeamListCard 状态调试:', {\n    currentTeam: currentTeam?.id,\n    currentTokenTeamId,\n    currentUserId,\n    hasTeamInToken,\n    hasRealCurrentTeam,\n    actualCurrentTeamId,\n    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,\n    initialStateCurrentUser: !!initialState?.currentUser,\n  });\n\n  // 获取团队列表数据\n  const fetchTeams = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const teamsData = await TeamService.getUserTeamsWithStats();\n      setTeams(teamsData);\n    } catch (error) {\n      console.error('获取团队列表失败:', error);\n      setError('获取团队列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    // 只有在用户已登录时才获取团队列表\n    if (initialState?.currentUser) {\n      fetchTeams();\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听全局状态变化，处理注销等情况\n  useEffect(() => {\n    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态\n    if (!initialState?.currentUser) {\n      setTeams([]);\n      setError(null);\n      setLoading(false);\n      setSwitchingTeamId(null);\n    }\n  }, [initialState?.currentUser]);\n\n  // 监听当前团队状态变化\n  useEffect(() => {\n    console.log('当前团队状态变化:', {\n      currentTeam: currentTeam?.id,\n      actualCurrentTeamId,\n      hasRealCurrentTeam,\n    });\n  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);\n\n  /**\n   * 去抖动搜索实现\n   *\n   * 使用useEffect监听searchText变化，在用户停止输入300ms后\n   * 更新debouncedSearchText，避免频繁的过滤操作，提升性能。\n   */\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setDebouncedSearchText(searchText);\n    }, 300);\n\n    return () => clearTimeout(timer);\n  }, [searchText]);\n\n  /**\n   * 团队过滤逻辑\n   *\n   * 使用useMemo优化过滤性能，只有在teams或debouncedSearchText变化时才重新计算。\n   * 实现不区分大小写的团队名称搜索过滤。\n   */\n  const filteredTeams = useMemo(() => {\n    if (!debouncedSearchText.trim()) {\n      return teams;\n    }\n\n    const searchLower = debouncedSearchText.toLowerCase().trim();\n    return teams.filter((team) =>\n      team.name.toLowerCase().includes(searchLower)\n    );\n  }, [teams, debouncedSearchText]);\n\n  // 创建团队功能已移至设置页面，此处不再需要处理函数\n\n  /**\n   * 搜索功能相关处理函数\n   */\n\n  /**\n   * 处理搜索输入变化\n   */\n  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchText(e.target.value);\n  }, []);\n\n  /**\n   * 处理搜索键盘事件\n   */\n  const handleSearchKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Escape') {\n      setSearchText('');\n      e.currentTarget.blur(); // 失去焦点\n    }\n  }, []);\n\n  /**\n   * 处理搜索清除\n   */\n  const handleSearchClear = useCallback(() => {\n    setSearchText('');\n  }, []);\n\n  /**\n   * 团队切换处理函数\n   *\n   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。\n   * 包括权限检查、API调用、状态更新、页面跳转等步骤。\n   *\n   * 切换流程：\n   * 1. 用户登录状态检查\n   * 2. 当前团队状态判断（避免重复切换）\n   * 3. 调用后端团队选择API\n   * 4. 验证切换结果\n   * 5. 更新本地Token和全局状态\n   * 6. 记录用户选择历史\n   * 7. 跳转到团队仪表盘\n   *\n   * 状态管理：\n   * - 设置切换加载状态（防止重复点击）\n   * - 更新全局用户和团队状态\n   * - 处理切换过程中的错误状态\n   *\n   * 错误处理：\n   * - 网络错误：显示网络连接提示\n   * - 权限错误：由响应拦截器统一处理\n   * - 业务错误：显示具体的错误信息\n   *\n   * @param teamId 要切换到的团队ID\n   * @param teamName 团队名称（用于显示消息）\n   */\n  const handleTeamSwitch = async (teamId: number, teamName: string) => {\n    /**\n     * 用户登录状态检查\n     *\n     * 确保用户已登录才能进行团队切换操作。\n     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。\n     */\n    if (!initialState?.currentUser) {\n      return;\n    }\n\n    try {\n      /**\n       * 设置切换状态\n       *\n       * 标记当前正在切换的团队ID，用于：\n       * 1. 在UI上显示加载状态\n       * 2. 防止用户重复点击\n       * 3. 提供视觉反馈\n       */\n      setSwitchingTeamId(teamId);\n\n      /**\n       * 当前团队检查\n       *\n       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，\n       * 避免不必要的API调用和Token更新。\n       */\n      if (teamId === actualCurrentTeamId) {\n        history.push('/dashboard');\n        return;\n      }\n\n      /**\n       * 执行团队切换API调用\n       *\n       * 调用后端的团队选择接口，后端会：\n       * 1. 验证用户是否有权限访问该团队\n       * 2. 生成包含新团队信息的JWT Token\n       * 3. 返回团队详细信息和切换状态\n       */\n      const response = await AuthService.selectTeam({ teamId });\n\n      /**\n       * 验证切换结果\n       *\n       * 检查后端返回的响应是否表示切换成功：\n       * - teamSelectionSuccess: 切换成功标识\n       * - team: 新团队的详细信息\n       * - team.id: 确认返回的团队ID与请求的一致\n       */\n      if (\n        response.teamSelectionSuccess &&\n        response.team &&\n        response.team.id === teamId\n      ) {\n        /**\n         * 记录用户选择历史\n         *\n         * 将用户的团队选择记录到本地存储，用于：\n         * - 下次登录时的默认团队选择\n         * - 用户行为分析\n         * - 提升用户体验\n         */\n        if (currentUserId) {\n          recordTeamSelection(currentUserId, teamId);\n        }\n\n        /**\n         * 异步更新全局状态\n         *\n         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。\n         * 使用异步更新避免阻塞页面跳转，提升用户体验。\n         *\n         * 更新流程：\n         * 1. 并行获取最新的用户信息和团队信息\n         * 2. 验证获取的团队信息是否正确\n         * 3. 更新全局状态\n         * 4. 处理更新过程中的错误\n         */\n        if (\n          initialState?.fetchTeamInfo &&\n          initialState?.fetchUserInfo &&\n          setInitialState\n        ) {\n          // 异步更新状态，不阻塞跳转\n          Promise.all([\n            initialState.fetchUserInfo(),\n            initialState.fetchTeamInfo(),\n          ])\n            .then(([currentUser, currentTeam]) => {\n              // 确认获取的团队信息与切换的团队一致\n              if (currentTeam && currentTeam.id === teamId) {\n                setInitialState({\n                  ...initialState,\n                  currentUser,\n                  currentTeam,\n                });\n              }\n            })\n            .catch((error) => {\n              console.error('更新 initialState 失败:', error);\n              // 状态更新失败不影响团队切换的核心功能\n            });\n        }\n\n        /**\n         * 页面跳转\n         *\n         * 切换成功后跳转到团队仪表盘。\n         * 路由守卫会验证新的Token并允许访问团队页面。\n         */\n        history.push('/dashboard');\n      } else {\n        /**\n         * 切换失败处理\n         *\n         * 如果后端返回的响应不符合预期，说明切换失败。\n         * 记录错误日志并提示用户重试。\n         */\n        // 团队切换响应异常，未返回正确的团队信息\n      }\n    } catch (error: any) {\n      /**\n       * 异常处理\n       *\n       * 处理团队切换过程中可能出现的各种异常：\n       * - 网络错误：连接超时、服务器不可达等\n       * - 权限错误：用户无权限访问该团队\n       * - 业务错误：团队不存在、状态异常等\n       *\n       * 错误处理策略：\n       * 1. 记录详细的错误日志用于调试\n       * 2. 响应拦截器已处理大部分错误消息\n       * 3. 只对网络错误显示通用提示\n       */\n      // 错误处理由响应拦截器统一处理\n    } finally {\n      /**\n       * 清理切换状态\n       *\n       * 无论切换成功还是失败，都要清除切换状态，\n       * 恢复UI的正常状态，允许用户进行下一次操作。\n       */\n      setSwitchingTeamId(null);\n    }\n  };\n\n  /**\n   * 处理团队管理\n   */\n  const handleTeamManagement = async (team: TeamDetailResponse) => {\n    try {\n      // 先切换到目标团队以确保有正确的权限\n      await AuthService.selectTeam({ teamId: team.id });\n      setSelectedTeam(team);\n      setTeamManagementModalVisible(true);\n    } catch (error) {\n      console.error('切换团队失败:', error);\n      message.error('无法打开团队管理');\n    }\n  };\n\n  /**\n   * 处理退出团队\n   */\n  const handleLeaveTeam = (team: TeamDetailResponse) => {\n    setSelectedTeam(team);\n    setLeaveTeamModalVisible(true);\n  };\n\n  /**\n   * 确认退出团队\n   */\n  const confirmLeaveTeam = async () => {\n    if (!selectedTeam) return;\n\n    try {\n      // 先切换到目标团队\n      await AuthService.selectTeam({ teamId: selectedTeam.id });\n\n      // 退出团队\n      await TeamService.leaveTeam();\n\n      // 重新获取团队列表\n      await fetchTeams();\n\n      // 更新全局状态，清除当前团队\n      if (setInitialState) {\n        await setInitialState((prevState) => ({\n          ...prevState,\n          currentTeam: undefined,\n        }));\n      }\n\n      message.success('已成功退出团队');\n      setLeaveTeamModalVisible(false);\n      setSelectedTeam(null);\n    } catch (error) {\n      console.error('退出团队失败:', error);\n      message.error('退出团队失败');\n    }\n  };\n\n\n\n  return (\n    <>\n      {/* 注入样式 */}\n      {/* <style dangerouslySetInnerHTML={{ __html: styles }} /> */}\n\n      <ProCard\n        title=\"团队列表\"\n        style={{\n          marginBottom: 24,\n          borderRadius: 8,\n          border: '1px solid #d9d9d9',\n        }}\n      >\n        {/* 搜索输入框 */}\n        {initialState?.currentUser && teams.length > 0 && (\n          <div style={{ marginBottom: 16 }}>\n            <Row gutter={[16, 0]}>\n              <Col xs={24} sm={24} md={16} lg={18} xl={20}>\n                <Input.Search\n                  placeholder=\"搜索团队名称...\"\n                  allowClear\n                  prefix={<SearchOutlined />}\n                  value={searchText}\n                  onChange={handleSearchChange}\n                  onKeyDown={handleSearchKeyDown}\n                  style={{ width: '100%' }}\n                  size=\"middle\"\n                />\n              </Col>\n            </Row>\n          </div>\n        )}\n        {error ? (\n          <Alert\n            message=\"团队列表加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {!initialState?.currentUser ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">请先登录以查看团队列表</Text>\n              </div>\n            ) : teams.length === 0 && !loading ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">暂无团队，请先加入或创建团队</Text>\n              </div>\n            ) : filteredTeams.length === 0 && debouncedSearchText.trim() ? (\n              <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n                <Text type=\"secondary\">未找到匹配的团队</Text>\n                <div style={{ marginTop: 8 }}>\n                  <Text type=\"secondary\" style={{ fontSize: 14 }}>\n                    尝试使用不同的关键词搜索\n                  </Text>\n                </div>\n              </div>\n            ) : (\n              <ProList\n                dataSource={filteredTeams}\n                split={false} /* 移除默认分割线 */\n                itemLayout=\"vertical\" /* 垂直布局 */\n                renderItem={(item) => (\n                  <ProCard\n                    className=\"team-item\"\n                    style={{\n                      marginBottom: 10,\n                      backgroundColor:\n                        actualCurrentTeamId === item.id\n                          ? '#f0f9ff'\n                          : '#fff',\n                      borderRadius: 8,\n                      width: '100%',\n                      borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,\n                      border:\n                        actualCurrentTeamId === item.id\n                          ? '1px solid #91caff'\n                          : '1px solid #d9d9d9',\n                   \n                      position: 'relative',\n                    }}\n                    >\n                      {/* 响应式布局 */}\n                      <Row\n                        gutter={[8, 8]}\n                        align=\"middle\"\n                        style={{ width: '100%' }}\n                      >\n                        {/* 左侧：团队信息 */}\n                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>\n                          <Flex vertical gap={8} className=\"team-info-wrap\"> {/* 减少垂直间距 */}\n                            {/* 团队名称行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\"> {/* 减少水平间距 */}\n                              <div\n                                style={{\n                                  cursor: 'pointer',\n                                  padding: '2px 4px',\n                                  borderRadius: 4,\n                                  transition: 'all 0.2s ease',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 6,\n                                }}\n                                onClick={() =>\n                                  handleTeamSwitch(item.id, item.name)\n                                }\n                                onMouseEnter={(e) => {\n                                  e.currentTarget.style.background =\n                                    'rgba(24, 144, 255, 0.05)';\n                                }}\n                                onMouseLeave={(e) => {\n                                  e.currentTarget.style.background =\n                                    'transparent';\n                                }}\n                              >\n                                <Text\n                                  strong\n                                  style={{\n                                    fontSize: 20, /* 增大团队名称字体 */\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#262626',\n                                    lineHeight: 1.3, /* 增加行高 */\n                                  }}\n                                >\n                                  {item.name}\n                                </Text>\n                                <RightOutlined\n                                  style={{\n                                    fontSize: 12, /* 增大图标大小 */\n                                    color:\n                                      actualCurrentTeamId === item.id\n                                        ? '#1890ff'\n                                        : '#8c8c8c',\n                                    verticalAlign: 'middle',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                  }}\n                                />\n                              </div>\n\n                              {/* 状态标识 */}\n                              {actualCurrentTeamId === item.id && (\n                                <span\n                                  style={{\n                                    background: '#1890ff',\n                                    color: 'white',\n                                    padding: '3px 8px', /* 增加内边距 */\n                                    borderRadius: 8,\n                                    fontSize: 12, /* 增大字体 */\n                                    fontWeight: 500,\n                                  }}\n                                >\n                                  当前\n                                </span>\n                              )}\n\n\n\n                              {switchingTeamId === item.id && (\n                                <Flex align=\"center\" gap={4}>\n                                  <Spin size=\"small\" />\n                                  <Text style={{ fontSize: 12, color: '#666' }}> {/* 增大字体 */}\n                                    切换中\n                                  </Text>\n                                </Flex>\n                              )}\n                            </Flex>\n\n                            {/* 团队基本信息 */}\n                            <Flex align=\"center\" gap={12} wrap=\"wrap\" className=\"team-meta-info\"> {/* 减少间距 */}\n                              <Tooltip\n                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}\n                              >\n                                <Flex align=\"center\" gap={4}> {/* 减少间距 */}\n                                  <ClockCircleOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 14 }} /* 增大图标 */\n                                  />\n                                  <Text\n                                    style={{ fontSize: 14, color: '#8c8c8c' }} /* 增大字体 */\n                                  >\n                                    创建: {new Date(\n                                      item.createdAt,\n                                    ).toLocaleDateString('zh-CN')}\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n\n                              {/* 加入日期 */}\n                              {item.assignedAt && (\n                                <Tooltip\n                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}\n                                >\n                                  <Flex align=\"center\" gap={4}> {/* 减少间距 */}\n                                    <UserOutlined\n                                      style={{ color: '#8c8c8c', fontSize: 14 }} /* 增大图标 */\n                                    />\n                                    <Text\n                                      style={{ fontSize: 14, color: '#8c8c8c' }} /* 增大字体 */\n                                    >\n                                      加入: {new Date(\n                                        item.assignedAt,\n                                      ).toLocaleDateString('zh-CN')}\n                                    </Text>\n                                  </Flex>\n                                </Tooltip>\n                              )}\n\n                              <Tooltip\n                                title={`团队成员: ${item.memberCount}人`}\n                              >\n                                <Flex align=\"center\" gap={4}> {/* 减少间距 */}\n                                  <TeamOutlined\n                                    style={{ color: '#8c8c8c', fontSize: 14 }} /* 增大图标 */\n                                  />\n                                  <Text\n                                    style={{ fontSize: 14, color: '#8c8c8c' }} /* 增大字体 */\n                                  >\n                                    {item.memberCount} 人\n                                  </Text>\n                                </Flex>\n                              </Tooltip>\n                            </Flex>\n\n                            {/* 状态标识行 */}\n                            <Flex align=\"center\" gap={8} wrap=\"wrap\" className=\"team-status-badges\"> {/* 减少间距 */}\n                              {/* 身份标识 */}\n                              <span\n                                style={{\n                                  background: item.isCreator ? '#722ed1' : '#1890ff',\n                                  color: 'white',\n                                  padding: '4px 8px',\n                                  borderRadius: 8,\n                                  fontSize: 12,\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 4,\n                                }}\n                              >\n                                {item.isCreator ? (\n                                  <>\n                                    <UserOutlined style={{ fontSize: 11 }} />\n                                    团队创建者\n                                  </>\n                                ) : (\n                                  <>\n                                    <TeamOutlined style={{ fontSize: 11 }} />\n                                    团队成员\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 用户状态标识 */}\n                              <span\n                                style={{\n                                  background: item.isActive ? '#52c41a' : '#ff4d4f',\n                                  color: 'white',\n                                  padding: '4px 8px', /* 增加内边距 */\n                                  borderRadius: 8,\n                                  fontSize: 12, /* 增大字体 */\n                                  fontWeight: 500,\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 2,\n                                }}\n                              >\n                                {item.isActive ? (\n                                  <>\n                                    <CheckCircleOutlined style={{ fontSize: 11 }} /> {/* 增大图标 */}\n                                    启用\n                                  </>\n                                ) : (\n                                  <>\n                                    <MinusCircleOutlined style={{ fontSize: 11 }} /> {/* 增大图标 */}\n                                    停用\n                                  </>\n                                )}\n                              </span>\n\n                              {/* 操作按钮 - 移动到状态信息之后 */}\n                              {item.isCreator ? (\n                                <Tooltip title=\"团队管理\">\n                                  <Button\n                                    type=\"text\"\n                                    size=\"small\"\n                                    icon={<SettingOutlined style={{ fontSize: 12 }} />}\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleTeamManagement(item);\n                                    }}\n                                    style={{\n                                      color: '#722ed1',\n                                      display: 'flex',\n                                      alignItems: 'center',\n                                      justifyContent: 'center',\n                                      width: 24,\n                                      height: 24,\n                                      padding: 0,\n                                      borderRadius: 4,\n                                      border: '1px solid #d3adf7',\n                                      background: 'rgba(114, 46, 209, 0.04)',\n                                      transition: 'all 0.2s ease',\n                                    }}\n                                    onMouseEnter={(e) => {\n                                      e.currentTarget.style.background = 'rgba(114, 46, 209, 0.1)';\n                                      e.currentTarget.style.borderColor = '#722ed1';\n                                    }}\n                                    onMouseLeave={(e) => {\n                                      e.currentTarget.style.background = 'rgba(114, 46, 209, 0.04)';\n                                      e.currentTarget.style.borderColor = '#d3adf7';\n                                    }}\n                                  />\n                                </Tooltip>\n                              ) : (\n                                <Tooltip title=\"退出团队\">\n                                  <Button\n                                    type=\"text\"\n                                    size=\"small\"\n                                    icon={<LogoutOutlined style={{ fontSize: 12 }} />}\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleLeaveTeam(item);\n                                    }}\n                                    style={{\n                                      color: '#ff4d4f',\n                                      display: 'flex',\n                                      alignItems: 'center',\n                                      justifyContent: 'center',\n                                      width: 24,\n                                      height: 24,\n                                      padding: 0,\n                                      borderRadius: 4,\n                                      border: '1px solid #ffccc7',\n                                      background: 'rgba(255, 77, 79, 0.04)',\n                                      transition: 'all 0.2s ease',\n                                    }}\n                                    onMouseEnter={(e) => {\n                                      e.currentTarget.style.background = 'rgba(255, 77, 79, 0.1)';\n                                      e.currentTarget.style.borderColor = '#ff4d4f';\n                                    }}\n                                    onMouseLeave={(e) => {\n                                      e.currentTarget.style.background = 'rgba(255, 77, 79, 0.04)';\n                                      e.currentTarget.style.borderColor = '#ffccc7';\n                                    }}\n                                  />\n                                </Tooltip>\n                              )}\n                            </Flex>\n                          </Flex>\n                        </Col>\n\n                        {/* 右侧：响应式指标卡片和操作按钮 */}\n                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>\n                          <Row\n                            gutter={[6, 6]} /* 减少间距 */\n                            justify={{ xs: 'start', md: 'end' }}\n                            align=\"middle\"\n                          >\n                            {/* 车辆资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f0f7ff',\n                                  border: '1px solid #d9e8ff',\n                                  borderRadius: 6,\n                                  padding: '6px 8px', /* 减少内边距 */\n                                  textAlign: 'center',\n                                  minWidth: '55px', /* 增加最小宽度 */\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={2}> {/* 减少间距 */}\n                                  <CarOutlined\n                                    style={{ color: '#1890ff', fontSize: 16 }} /* 增大图标 */\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 18, /* 增大数字字体 */\n                                      color: '#1890ff',\n                                      lineHeight: 1.2, /* 增加行高 */\n                                    }}\n                                  >\n                                    {item.stats?.vehicles || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}\n                                    车辆\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 人员资源 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#f6ffed',\n                                  border: '1px solid #d1f0be',\n                                  borderRadius: 6,\n                                  padding: '6px 8px', /* 减少内边距 */\n                                  textAlign: 'center',\n                                  minWidth: '55px', /* 保持最小宽度 */\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={2}> {/* 减少间距 */}\n                                  <UserOutlined\n                                    style={{ color: '#52c41a', fontSize: 16 }} /* 增大图标 */\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 18, /* 增大数字字体 */\n                                      color: '#52c41a',\n                                      lineHeight: 1.2, /* 增加行高 */\n                                    }}\n                                  >\n                                    {item.stats?.personnel || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}\n                                    人员\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 临期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff7e6',\n                                  border: '1px solid #ffd666',\n                                  borderRadius: 6,\n                                  padding: '6px 8px', /* 减少内边距 */\n                                  textAlign: 'center',\n                                  minWidth: '55px', /* 保持最小宽度 */\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={2}> {/* 减少间距 */}\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#faad14', fontSize: 16 }} /* 增大图标 */\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 18, /* 增大数字字体 */\n                                      color: '#faad14',\n                                      lineHeight: 1.2, /* 增加行高 */\n                                    }}\n                                  >\n                                    {item.stats?.expiring || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}\n                                    临期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n                            {/* 逾期事项 */}\n                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>\n                              <div\n                                style={{\n                                  background: '#fff1f0',\n                                  border: '1px solid #ffccc7',\n                                  borderRadius: 6,\n                                  padding: '6px 8px', /* 减少内边距 */\n                                  textAlign: 'center',\n                                  minWidth: '55px', /* 保持最小宽度 */\n                                }}\n                              >\n                                <Flex vertical align=\"center\" gap={2}> {/* 减少间距 */}\n                                  <ExclamationCircleOutlined\n                                    style={{ color: '#ff4d4f', fontSize: 16 }} /* 增大图标 */\n                                  />\n                                  <Text\n                                    strong\n                                    style={{\n                                      fontSize: 18, /* 增大数字字体 */\n                                      color: '#ff4d4f',\n                                      lineHeight: 1.2, /* 增加行高 */\n                                    }}\n                                  >\n                                    {item.stats?.overdue || 0}\n                                  </Text>\n                                  <Text style={{ fontSize: 11, color: '#666' }}> {/* 增大标签字体 */}\n                                    逾期\n                                  </Text>\n                                </Flex>\n                              </div>\n                            </Col>\n\n\n                          </Row>\n                        </Col>\n                      </Row>\n                    </ProCard>\n                )}\n              />\n            )}\n          </Spin>\n        )}\n      </ProCard>\n\n      {/* 团队管理模态框 */}\n      <TeamManagementModal\n        visible={teamManagementModalVisible}\n        onCancel={() => {\n          setTeamManagementModalVisible(false);\n          setSelectedTeam(null);\n        }}\n        team={selectedTeam}\n        onRefresh={fetchTeams}\n      />\n\n      {/* 退出团队确认模态框 */}\n      <Modal\n        title=\"退出团队\"\n        open={leaveTeamModalVisible}\n        onCancel={() => {\n          setLeaveTeamModalVisible(false);\n          setSelectedTeam(null);\n        }}\n        footer={[\n          <Button\n            key=\"cancel\"\n            onClick={() => {\n              setLeaveTeamModalVisible(false);\n              setSelectedTeam(null);\n            }}\n          >\n            取消\n          </Button>,\n          <Button\n            key=\"leave\"\n            type=\"primary\"\n            danger\n            onClick={confirmLeaveTeam}\n          >\n            确认退出\n          </Button>,\n        ]}\n      >\n        <div style={{ textAlign: 'center', padding: '20px 0' }}>\n          <p>确定要退出团队 <strong>{selectedTeam?.name}</strong> 吗？</p>\n          <p style={{ color: '#ff4d4f' }}>退出后您将无法访问该团队的资源和数据</p>\n        </div>\n      </Modal>\n\n      {/* 创建团队功能已移至设置页面 */}\n    </>\n  );\n};\n\nexport default TeamListCard;\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ModalForm, ProList, ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = () => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = (personalTasks || []).filter((task) => {\n    // 根据标签过滤\n    if (activeTab === 'pending' && task.status === 1) return false;\n    if (activeTab === 'completed' && task.status === 0) return false;\n\n    // 根据搜索文本过滤\n    if (\n      searchText &&\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  return (\n    <ProCard\n      title=\"待办事项/任务列表\"\n      style={{\n        borderRadius: 8,\n        height: 'fit-content',\n        minHeight: '600px', // 确保右列有足够的高度\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n    >\n      {/* 任务管理界面头部区域 */}\n      <div\n        style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: '#fafbfc',\n          borderRadius: 8,\n          border: '1px solid #f0f0f0',\n        }}\n      >\n        {/* 优化的统计信息区域 */}\n        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n          {/* 左侧：优先级统计 */}\n          <Col xs={24} sm={14} md={14} lg={16} xl={18}>\n            <Flex align=\"center\" gap={12} wrap=\"wrap\">\n              {/* 高优先级 */}\n              <div\n                style={{\n                  background: '#fff2f0',\n                  border: '1px solid #ffccc7',\n                  borderRadius: 6,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8,\n                  minWidth: 100,\n                }}\n              >\n                <div\n                  style={{\n                    width: 8,\n                    height: 8,\n                    borderRadius: '50%',\n                    background: '#ff4d4f',\n                  }}\n                />\n                <Text style={{ fontSize: 12, color: '#8c8c8c', marginRight: 4 }}>\n                  高优先级\n                </Text>\n                <Text style={{ fontSize: 14, fontWeight: 600, color: '#cf1322' }}>\n                  {todoStats.highPriorityCount}\n                </Text>\n              </div>\n\n              {/* 中优先级 */}\n              <div\n                style={{\n                  background: '#fffbe6',\n                  border: '1px solid #ffe58f',\n                  borderRadius: 6,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8,\n                  minWidth: 100,\n                }}\n              >\n                <div\n                  style={{\n                    width: 8,\n                    height: 8,\n                    borderRadius: '50%',\n                    background: '#faad14',\n                  }}\n                />\n                <Text style={{ fontSize: 12, color: '#8c8c8c', marginRight: 4 }}>\n                  中优先级\n                </Text>\n                <Text style={{ fontSize: 14, fontWeight: 600, color: '#d48806' }}>\n                  {todoStats.mediumPriorityCount}\n                </Text>\n              </div>\n\n              {/* 低优先级 */}\n              <div\n                style={{\n                  background: '#fafafa',\n                  border: '1px solid #d9d9d9',\n                  borderRadius: 6,\n                  padding: '8px 12px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 8,\n                  minWidth: 100,\n                }}\n              >\n                <div\n                  style={{\n                    width: 8,\n                    height: 8,\n                    borderRadius: '50%',\n                    background: '#8c8c8c',\n                  }}\n                />\n                <Text style={{ fontSize: 12, color: '#8c8c8c', marginRight: 4 }}>\n                  低优先级\n                </Text>\n                <Text style={{ fontSize: 14, fontWeight: 600, color: '#595959' }}>\n                  {todoStats.lowPriorityCount}\n                </Text>\n              </div>\n            </Flex>\n          </Col>\n\n          {/* 右侧：完成率 */}\n          <Col xs={24} sm={10} md={10} lg={8} xl={6}>\n            <div\n              style={{\n                background: '#f6ffed',\n                border: '1px solid #b7eb8f',\n                borderRadius: 8,\n            \n                height: '100%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n              }}\n            >\n              <Tooltip\n                title={`完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`}\n              >\n                <Flex align=\"center\" gap={12}>\n                  <Text style={{ fontSize: 12, color: '#8c8c8c' }}>\n                    完成率\n                  </Text>\n                  <Progress\n                    percent={todoStats.completionPercentage}\n                    size=\"small\"\n                    style={{ width: 80 }}\n                    strokeColor=\"#52c41a\"\n                    showInfo={false}\n                  />\n                  <Text\n                    style={{\n                      fontSize: 16,\n                      fontWeight: 600,\n                      color: '#389e0d',\n                    }}\n                  >\n                    {todoStats.completionPercentage}%\n                  </Text>\n                </Flex>\n              </Tooltip>\n            </div>\n          </Col>\n        </Row>\n\n        {/* 第二行：左侧搜索功能，右侧添加新任务按钮 */}\n        <Row gutter={[16, 0]}>\n          <Col xs={24} sm={24} md={16} lg={18} xl={20}>\n            <Input.Search\n              placeholder=\"搜索任务...\"\n              allowClear\n              prefix={<SearchOutlined />}\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: '100%' }}\n              size=\"middle\"\n            />\n          </Col>\n\n          <Col xs={24} sm={24} md={8} lg={6} xl={4}>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => {\n                setEditingTodoId(null);\n                todoForm.resetFields();\n                setTodoModalVisible(true);\n              }}\n              style={{\n                background: '#1890ff',\n                borderColor: '#1890ff',\n                boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                fontWeight: 500,\n                width: '100%',\n              }}\n              size=\"middle\"\n            >\n              添加新任务\n            </Button>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 第二行：标签页 */}\n      <Tabs\n        activeKey={activeTab}\n        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n        size=\"middle\"\n        style={{ marginBottom: 8 }}\n      >\n        <TabPane tab=\"全部\" key=\"all\" />\n        <TabPane tab=\"待处理\" key=\"pending\" />\n        <TabPane tab=\"已完成\" key=\"completed\" />\n      </Tabs>\n\n      {/* 待办事项列表 */}\n      {error ? (\n        <Alert\n          message=\"TODO数据加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n      ) : (\n        <Spin spinning={loading}>\n          <ProList\n            dataSource={filteredPersonalTasks}\n            renderItem={(item) => {\n              return (\n                <div\n                  className=\"todo-item\"\n                  style={{\n                    padding: '10px 16px',\n                    marginBottom: 12,\n                    borderRadius: 8,\n                    background: '#fff',\n                    opacity: item.status === 1 ? 0.7 : 1,\n                    borderLeft: `3px solid ${\n                      item.status === 1\n                        ? '#52c41a'\n                        : item.priority === 3\n                          ? '#ff4d4f'\n                          : item.priority === 2\n                            ? '#faad14'\n                            : '#8c8c8c'\n                    }`,\n                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',\n                  }}\n                >\n                  <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n                    {/* 左侧状态和优先级指示器 */}\n                    <Flex vertical align=\"center\">\n                      {item.status === 1 ? (\n                        <Flex\n                          align=\"center\"\n                          justify=\"center\"\n                          style={{\n                            width: 22,\n                            height: 22,\n                            borderRadius: '50%',\n                            background: '#52c41a',\n                          }}\n                        >\n                          <CheckOutlined\n                            style={{ color: '#fff', fontSize: 12 }}\n                          />\n                        </Flex>\n                      ) : (\n                        <div\n                          style={{\n                            width: 18,\n                            height: 18,\n                            borderRadius: '50%',\n                            border: `2px solid ${\n                              item.priority === 3\n                                ? '#ff4d4f'\n                                : item.priority === 2\n                                  ? '#faad14'\n                                  : '#8c8c8c'\n                            }`,\n                          }}\n                        />\n                      )}\n\n                      <div\n                        style={{\n                          width: 2,\n                          height: 24,\n                          background: '#f0f0f0',\n                          marginTop: 4,\n                        }}\n                      />\n                    </Flex>\n\n                    {/* 任务信息区 */}\n                    <Flex vertical style={{ flex: 1 }}>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          fontWeight: item.priority === 3 ? 500 : 'normal',\n                          textDecoration:\n                            item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}\n                      >\n                        {item.title}\n                      </Text>\n\n                      {/* 显示创建日期 */}\n                      <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\n                        <CalendarOutlined\n                          style={{\n                            fontSize: 12,\n                            color: '#8c8c8c',\n                          }}\n                        />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          创建于:{' '}\n                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}\n                        </Text>\n                      </Space>\n                    </Flex>\n\n                    {/* 操作按钮区 */}\n                    <Dropdown\n                      trigger={['click']}\n                      menu={{\n                        items: [\n                          {\n                            key: 'complete',\n                            label:\n                              item.status === 1 ? '标记未完成' : '标记完成',\n                            icon: (\n                              <CheckOutlined\n                                style={{\n                                  color:\n                                    item.status === 1 ? '#8c8c8c' : '#52c41a',\n                                  fontSize: 14,\n                                }}\n                              />\n                            ),\n                          },\n                          {\n                            key: 'edit',\n                            label: '编辑任务',\n                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,\n                          },\n                          {\n                            key: 'delete',\n                            label: '删除任务',\n                            icon: (\n                              <DeleteOutlined style={{ color: '#ff4d4f' }} />\n                            ),\n                            danger: true,\n                          },\n                        ],\n                        onClick: ({ key }) => {\n                          if (key === 'complete') {\n                            handleToggleTodoStatus(item.id);\n                          } else if (key === 'edit') {\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          } else if (key === 'delete') {\n                            handleDeleteTodo(item.id);\n                          }\n                        },\n                      }}\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<MoreOutlined />}\n                        style={{ width: 32, height: 32 }}\n                      />\n                    </Dropdown>\n                  </Flex>\n                </div>\n              );\n            }}\n          />\n\n          {/* 待办事项表单模态框 */}\n          <ModalForm\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onOpenChange={(visible) => {\n              setTodoModalVisible(visible);\n              if (!visible) {\n                setEditingTodoId(null);\n                todoForm.resetFields();\n              }\n            }}\n            form={todoForm}\n            layout=\"vertical\"\n            onFinish={handleAddOrUpdateTodo}\n            autoComplete=\"off\"\n            width={500}\n            modalProps={{\n              centered: true,\n              destroyOnClose: true,\n              maskClosable: true,\n              keyboard: true,\n              forceRender: false,\n            }}\n            submitter={{\n              searchConfig: {\n                submitText: editingTodoId ? '更新任务' : '创建任务',\n                resetText: '取消',\n              },\n              submitButtonProps: {\n                style: {\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                },\n                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,\n              },\n              resetButtonProps: {\n                style: {\n                  borderColor: '#d9d9d9',\n                },\n              },\n              onReset: () => {\n                setTodoModalVisible(false);\n                setEditingTodoId(null);\n                todoForm.resetFields();\n              },\n            }}\n            preserve={false}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"任务名称\"\n              rules={[{ required: true, message: '请输入任务名称' }]}\n            >\n              <Input\n                placeholder=\"请输入任务名称\"\n                size=\"large\"\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"priority\"\n              label=\"优先级\"\n              initialValue={2}\n              rules={[{ required: true, message: '请选择优先级' }]}\n            >\n              <Select\n                size=\"large\"\n                options={[\n                  { value: 3, label: '高优先级' },\n                  { value: 2, label: '中优先级' },\n                  { value: 1, label: '低优先级' },\n                ]}\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n          </ModalForm>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default TodoManagement;\n", "import { SettingOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';\nimport {\n  Avatar,\n  Form,\n  Input,\n  Modal,\n  Space,\n  Tabs,\n  Typography,\n  message,\n} from 'antd';\nimport React, { useEffect, useState } from 'react';\nimport type { UserProfileDetailResponse } from '@/types/user';\n\nconst { Title, Text } = Typography;\n\ninterface UnifiedSettingsModalProps {\n  visible: boolean;\n  onCancel: () => void;\n  onSuccess?: () => void;\n  userInfo: UserProfileDetailResponse;\n}\n\n/**\n * 统一设置Modal组件\n *\n * 提供个人信息修改和新建团队功能的统一界面。\n * 使用Tab页面结构组织不同的设置功能。\n *\n * 主要功能：\n * 1. 个人信息修改Tab：编辑用户基本信息\n * 2. 新建团队Tab：创建新团队\n *\n * Props:\n * - visible: 控制Modal显示/隐藏\n * - onCancel: 取消操作回调\n * - onSuccess: 操作成功回调\n * - userInfo: 当前用户信息\n */\nconst UnifiedSettingsModal: React.FC<UnifiedSettingsModalProps> = ({\n  visible,\n  onCancel,\n  onSuccess,\n  userInfo,\n}) => {\n  const [personalForm] = Form.useForm();\n  const [teamForm] = Form.useForm();\n  const [activeTab, setActiveTab] = useState('personal');\n\n  // 当Modal打开时，填充个人信息表单数据\n  useEffect(() => {\n    if (visible && userInfo) {\n      personalForm.setFieldsValue({\n        name: userInfo.name,\n        email: userInfo.email,\n        telephone: userInfo.telephone,\n        position: userInfo.position,\n      });\n    }\n  }, [visible, userInfo, personalForm]);\n\n  // 处理个人信息提交\n  const handlePersonalSubmit = async () => {\n    try {\n      const values = await personalForm.validateFields();\n      console.log('更新个人信息:', values);\n      \n      // TODO: 调用更新用户信息的API\n      // await UserService.updateUserProfile(values);\n      \n      message.success('个人信息更新成功！');\n      onSuccess?.();\n      onCancel();\n    } catch (error) {\n      console.error('更新个人信息失败:', error);\n      message.error('更新个人信息失败，请稍后重试');\n    }\n  };\n\n  // 处理团队创建提交\n  const handleTeamSubmit = async () => {\n    try {\n      const values = await teamForm.validateFields();\n      console.log('创建团队:', values);\n      \n      // TODO: 调用创建团队的API\n      // await TeamService.createTeam(values);\n      \n      message.success('团队创建成功！');\n      teamForm.resetFields();\n      onSuccess?.();\n      onCancel();\n    } catch (error) {\n      console.error('创建团队失败:', error);\n      message.error('创建团队失败，请稍后重试');\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    personalForm.resetFields();\n    teamForm.resetFields();\n    setActiveTab('personal');\n    onCancel();\n  };\n\n  // 根据当前Tab决定提交操作\n  const handleOk = () => {\n    if (activeTab === 'personal') {\n      handlePersonalSubmit();\n    } else {\n      handleTeamSubmit();\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <Space align=\"center\">\n          <SettingOutlined style={{ fontSize: 18, color: '#1890ff' }} />\n          <Title level={4} style={{ margin: 0, fontSize: 16 }}>\n            设置\n          </Title>\n        </Space>\n      }\n      open={visible}\n      onOk={handleOk}\n      onCancel={handleCancel}\n      okText={activeTab === 'personal' ? '保存设置' : '创建团队'}\n      cancelText=\"取消\"\n \n      destroyOnClose\n   \n    >\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        style={{ marginTop: -8 }}\n        items={[\n          {\n            key: 'personal',\n            label: (\n              <Space align=\"center\">\n                <UserOutlined />\n                <span>个人信息</span>\n              </Space>\n            ),\n            children: (\n              <div>\n                <div style={{ marginBottom: 16 }}>\n                  <Text style={{ color: '#8c8c8c', fontSize: 14 }}>\n                    编辑您的个人信息和偏好设置\n                  </Text>\n                </div>\n\n                <Form\n                  form={personalForm}\n                  layout=\"vertical\"\n                  requiredMark={false}\n                  autoComplete=\"off\"\n                >\n                  {/* 姓名 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        姓名\n                      </Text>\n                    }\n                    name=\"name\"\n                    rules={[\n                      { required: true, message: '请输入姓名' },\n                      { min: 2, message: '姓名至少2个字符' },\n                      { max: 20, message: '姓名不能超过20个字符' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入姓名\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n\n                  {/* 职位 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        职位\n                      </Text>\n                    }\n                    name=\"position\"\n                    rules={[\n                      { max: 50, message: '职位不能超过50个字符' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入职位（可选）\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n\n                  {/* 邮箱 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        邮箱\n                      </Text>\n                    }\n                    name=\"email\"\n                    rules={[\n                      { required: true, message: '请输入邮箱' },\n                      { type: 'email', message: '请输入有效的邮箱地址' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入邮箱\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n\n                  {/* 电话 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 14 }}>\n                        电话\n                      </Text>\n                    }\n                    name=\"telephone\"\n                    rules={[\n                      { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号码' },\n                    ]}\n                  >\n                    <Input\n                      placeholder=\"请输入电话（可选）\"\n                      style={{\n                        borderRadius: 6,\n                        fontSize: 14,\n                      }}\n                    />\n                  </Form.Item>\n                </Form>\n              </div>\n            ),\n          },\n          {\n            key: 'team',\n            label: (\n              <Space align=\"center\">\n                <TeamOutlined />\n                <span>新建团队</span>\n              </Space>\n            ),\n            children: (\n              <div>\n                <div style={{ marginBottom: 24, textAlign: 'center' }}>\n                  <Text style={{ color: '#8c8c8c', fontSize: 14, lineHeight: 1.6 }}>\n                    创建一个新的团队来协作管理项目和任务\n                  </Text>\n                </div>\n\n                <Form\n                  form={teamForm}\n                  layout=\"vertical\"\n                  requiredMark={false}\n                  autoComplete=\"off\"\n                >\n                  {/* 团队名称 */}\n                  <Form.Item\n                    label={\n                      <Text style={{ fontWeight: 600, fontSize: 15 }}>\n                        团队名称\n                      </Text>\n                    }\n                    name=\"teamName\"\n                    rules={[\n                      { required: true, message: '请输入团队名称' },\n                      { min: 2, message: '团队名称至少2个字符' },\n                      { max: 50, message: '团队名称不能超过50个字符' },\n                    ]}\n                    style={{ marginBottom: 0 }}\n                  >\n                    <Input\n                      placeholder=\"请输入团队名称\"\n                      size=\"large\"\n                      style={{\n                        borderRadius: 8,\n                        fontSize: 15,\n                        padding: '12px 16px',\n                        border: '2px solid #d9d9d9',\n                        transition: 'all 0.3s ease',\n                      }}\n                      onFocus={(e) => {\n                        e.target.style.borderColor = '#1890ff';\n                        e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.1)';\n                      }}\n                      onBlur={(e) => {\n                        e.target.style.borderColor = '#d9d9d9';\n                        e.target.style.boxShadow = 'none';\n                      }}\n                    />\n                  </Form.Item>\n                </Form>\n              </div>\n            ),\n          },\n        ]}\n      />\n    </Modal>\n  );\n};\n\nexport default UnifiedSettingsModal;\n", "\nimport \"F:/Project/teamAuth/frontend/src/pages/personal-center/UserInfoPopover.module.css?modules\";\nexport default {\"trigger\": `trigger-vd13A828`,\"email\": `email-YepxYkkf`,\"phone\": `phone-EFJPU7Vm`,\"label\": `label-fY2ApLsi`,\"value\": `value-ClCU1LzT`,\"popoverContent\": `popoverContent-EhVEGF9V`,\"divider\": `divider-kogfDmru`,\"lastLogin\": `lastLogin-_0fqwZFk`,\"popoverTitle\": `popoverTitle-gZ0PK1Dm`,\"settingIcon\": `settingIcon-JoqAnTNp`,\"team\": `team-iiXX2L4w`,\"iconWrapper\": `iconWrapper-HCDP4HDG`,\"icon\": `icon-z1rS9q3h`,\"fadeIn\": `fadeIn-Bxe6iemy`,\"register\": `register-NbpyKnYq`,\"infoItem\": `infoItem-TRvgqB7k`,\"infoContent\": `infoContent-AvzO53H-`,\"questionIcon\": `questionIcon-GFk04_0Q`}\n", "import {\n  MailOutlined,\n  PhoneOutlined,\n  CalendarOutlined,\n  ClockCircleOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport {\n  Popover,\n  Space,\n  Typography,\n  Divider,\n} from 'antd';\nimport React from 'react';\nimport type { UserProfileDetailResponse } from '@/types/api';\nimport styles from './UserInfoPopover.module.css';\n\nconst { Text } = Typography;\n\ninterface UserInfoPopoverProps {\n  userInfo: UserProfileDetailResponse;\n  children: React.ReactNode;\n}\n\n/**\n * 用户信息气泡卡片组件\n *\n * 在用户名上显示详细的用户信息，包括电话、邮箱、注册时间等。\n * 采用Popover组件实现悬浮显示效果。\n *\n * 主要功能：\n * 1. 显示用户邮箱\n * 2. 显示用户电话\n * 3. 显示注册时间\n * 4. 显示最后登录时间\n * 5. 显示最后登录团队\n *\n * 使用方式：\n * <UserInfoPopover userInfo={userInfo}>\n *   <span>用户名</span>\n * </UserInfoPopover>\n */\nconst UserInfoPopover: React.FC<UserInfoPopoverProps> = ({\n  userInfo,\n  children,\n}) => {\n  const popoverContent = (\n    <div className={styles.popoverContent}>\n      <Space direction=\"vertical\" size={12} style={{ width: '100%' }}>\n        {/* 联系信息 */}\n        {userInfo.email && (\n          <div className={`${styles.infoItem} ${styles.email}`}>\n            <div className={styles.iconWrapper}>\n              <MailOutlined className={styles.icon} style={{ color: '#1890ff' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                邮箱\n              </Text>\n              <Text className={styles.value} copyable>\n                {userInfo.email}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.telephone && (\n          <div className={`${styles.infoItem} ${styles.phone}`}>\n            <div className={styles.iconWrapper}>\n              <PhoneOutlined className={styles.icon} style={{ color: '#52c41a' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                电话\n              </Text>\n              <Text className={styles.value} copyable>\n                {userInfo.telephone}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {(userInfo.email || userInfo.telephone) && userInfo.registerDate && (\n          <Divider className={styles.divider} />\n        )}\n\n        {/* 时间信息 */}\n        {userInfo.registerDate && (\n          <div className={`${styles.infoItem} ${styles.register}`}>\n            <div className={styles.iconWrapper}>\n              <CalendarOutlined className={styles.icon} style={{ color: '#722ed1' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                注册时间\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.registerDate}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.lastLoginTime && (\n          <div className={`${styles.infoItem} ${styles.lastLogin}`}>\n            <div className={styles.iconWrapper}>\n              <ClockCircleOutlined className={styles.icon} style={{ color: '#fa8c16' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                最后登录\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.lastLoginTime}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {userInfo.lastLoginTeam && (\n          <div className={`${styles.infoItem} ${styles.team}`}>\n            <div className={styles.iconWrapper}>\n              <TeamOutlined className={styles.icon} style={{ color: '#13c2c2' }} />\n            </div>\n            <div className={styles.infoContent}>\n              <Text type=\"secondary\" className={styles.label}>\n                登录团队\n              </Text>\n              <Text className={styles.value}>\n                {userInfo.lastLoginTeam}\n              </Text>\n            </div>\n          </div>\n        )}\n      </Space>\n    </div>\n  );\n\n  return (\n    <Popover\n      content={popoverContent}\n      title={\n        <div className={styles.popoverTitle}>\n          <Text strong>用户详细信息</Text>\n        </div>\n      }\n      trigger={[\"hover\", \"click\"]}\n      placement=\"bottomLeft\"\n      styles={{\n        body: {\n          padding: '16px 20px',\n          borderRadius: '12px',\n          background: '#ffffff',\n          maxWidth: '380px',\n          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08)',\n          border: '1px solid rgba(0, 0, 0, 0.06)',\n        },\n      }}\n      arrow={{\n        pointAtCenter: true,\n      }}\n      mouseEnterDelay={0.3}\n      mouseLeaveDelay={0.1}\n      fresh={false}\n      zIndex={1060}\n    >\n      <span className={styles.trigger}>\n        {children}\n      </span>\n    </Popover>\n  );\n};\n\nexport default UserInfoPopover;\n", "/**\n * 团队管理模态框组件\n *\n * 功能特性：\n * - 团队成员列表和管理\n * - 团队邀请功能\n * - 团队信息编辑\n * - 团队删除功能\n * - 权限控制，只有团队创建者可以访问管理功能\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Modal,\n  Card,\n  Button,\n  Space,\n  Input,\n  Tag,\n  Typography,\n  Popconfirm,\n  Select,\n  Tooltip,\n  Row,\n  Col,\n  Form,\n  message,\n  Avatar,\n  Dropdown,\n  Alert,\n  Spin,\n  Tabs\n} from 'antd';\nimport { ProTable } from '@ant-design/pro-components';\nimport {\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  PlusOutlined,\n  TeamOutlined,\n  EditOutlined,\n  SaveOutlined,\n  ExclamationCircleOutlined,\n  UserAddOutlined,\n  MoreOutlined\n} from '@ant-design/icons';\nimport type { ProColumns } from '@ant-design/pro-components';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport { InvitationService } from '@/services/invitation';\nimport type { \n  TeamDetailResponse, \n  TeamMemberResponse, \n  TeamInvitationResponse,\n  UpdateTeamRequest,\n  InviteMembersRequest\n} from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { TabPane } = Tabs;\n\ninterface TeamManagementModalProps {\n  visible: boolean;\n  onCancel: () => void;\n  team: TeamDetailResponse | null;\n  onRefresh: () => void;\n}\n\nconst TeamManagementModal: React.FC<TeamManagementModalProps> = ({\n  visible,\n  onCancel,\n  team,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  \n  // 团队编辑相关状态\n  const [editModalVisible, setEditModalVisible] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [form] = Form.useForm();\n  \n  // 团队删除相关状态\n  const [deleteModalVisible, setDeleteModalVisible] = useState(false);\n  const [deleteConfirmText, setDeleteConfirmText] = useState('');\n  const [deleting, setDeleting] = useState(false);\n  \n  // 邀请成员相关状态\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  useEffect(() => {\n    if (visible && team) {\n      fetchMembers();\n      fetchInvitations();\n    }\n  }, [visible, team]);\n\n  // 获取团队成员列表\n  const fetchMembers = async () => {\n    if (!team) return;\n    \n    try {\n      setLoading(true);\n      const response = await TeamService.getTeamMembers();\n      setMembers(response.list || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    if (!team) return;\n\n    try {\n      const response = await InvitationService.getCurrentTeamInvitations();\n      setInvitations(response || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n    }\n  };\n\n  // 移除团队成员\n  const handleRemoveMember = async (memberId: number, memberName: string) => {\n    try {\n      await TeamService.removeMember(memberId);\n      message.success(`已移除成员 ${memberName}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 邀请成员\n  const handleInviteMembers = async (values: { emails: string; message?: string }) => {\n    try {\n      const emailList = values.emails.split(',').map(email => email.trim()).filter(email => email);\n      const request: InviteMembersRequest = {\n        emails: emailList,\n        message: values.message\n      };\n      \n      await TeamService.inviteMembers(request);\n      message.success('邀请已发送');\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchInvitations();\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 更新团队信息\n  const handleUpdateTeam = async (values: UpdateTeamRequest) => {\n    if (!team) return;\n    \n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditModalVisible(false);\n      form.resetFields();\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队信息失败:', error);\n      message.error('更新团队信息失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  // 删除团队\n  const handleDeleteTeam = async () => {\n    if (!team || deleteConfirmText !== team.name) {\n      message.error('请输入正确的团队名称');\n      return;\n    }\n    \n    try {\n      setDeleting(true);\n      await TeamService.deleteCurrentTeam();\n      message.success('团队已删除');\n      setDeleteModalVisible(false);\n      onCancel();\n      onRefresh();\n    } catch (error) {\n      console.error('删除团队失败:', error);\n      message.error('删除团队失败');\n    } finally {\n      setDeleting(false);\n    }\n  };\n\n  // 权限检查\n  const hasManagePermission = team?.isCreator || false;\n\n  if (!team) {\n    return null;\n  }\n\n  // 成员表格列定义\n  const memberColumns: ProColumns<TeamMemberResponse>[] = [\n    {\n      title: '成员信息',\n      dataIndex: 'name',\n      key: 'name',\n      render: (_, record) => (\n        <Space>\n          <Avatar size=\"small\" icon={<UserOutlined />}>\n            {record.name?.charAt(0)?.toUpperCase()}\n          </Avatar>\n          <div>\n            <div style={{ fontWeight: 500 }}>{record.name}</div>\n            <div style={{ fontSize: 12, color: '#666' }}>{record.email}</div>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '角色',\n      dataIndex: 'isCreator',\n      key: 'role',\n      render: (_, record) => (\n        <Tag\n          icon={record.isCreator ? <CrownOutlined /> : <UserOutlined />}\n          color={record.isCreator ? 'gold' : 'blue'}\n        >\n          {record.isCreator ? '管理员' : '成员'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-',\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'isActive',\n      render: (isActive) => (\n        <Tag color={isActive ? 'success' : 'error'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n  ];\n\n  if (hasManagePermission) {\n    memberColumns.push({\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          {!record.isCreator && (\n            <Popconfirm\n              title=\"确定要移除这个成员吗？\"\n              onConfirm={() => handleRemoveMember(record.id, record.name)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          )}\n        </Space>\n      ),\n    });\n  }\n\n  // 邀请记录表格列定义\n  const invitationColumns: ProColumns<TeamInvitationResponse>[] = [\n    {\n      title: '邀请信息',\n      dataIndex: 'inviteeEmail',\n      key: 'inviteeEmail',\n      render: (_, record) => (\n        <div>\n          <div style={{ fontWeight: 500 }}>{record.inviteeEmail}</div>\n          {record.inviteeName && (\n            <div style={{ fontSize: 12, color: '#666' }}>{record.inviteeName}</div>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <InvitationStatusComponent status={status} />\n      ),\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),\n    },\n    {\n      title: '邀请人',\n      dataIndex: 'inviterName',\n      key: 'inviterName',\n    },\n  ];\n\n  if (hasManagePermission) {\n    invitationColumns.push({\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          {record.canBeCancelled && (\n            <Popconfirm\n              title=\"确定要取消这个邀请吗？\"\n              onConfirm={() => handleCancelInvitation(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                取消\n              </Button>\n            </Popconfirm>\n          )}\n        </Space>\n      ),\n    });\n  }\n\n  // 取消邀请\n  const handleCancelInvitation = async (invitationId: number) => {\n    try {\n      await InvitationService.cancelInvitation(invitationId);\n      message.success('邀请已取消');\n      fetchInvitations();\n    } catch (error) {\n      console.error('取消邀请失败:', error);\n      message.error('取消邀请失败');\n    }\n  };\n\n  return (\n    <>\n      <Modal\n        title={\n          <Space>\n            <TeamOutlined />\n            <span>团队管理 - {team.name}</span>\n          </Space>\n        }\n        open={visible}\n        onCancel={onCancel}\n        width={1200}\n        footer={null}\n        destroyOnClose\n      >\n        <Tabs defaultActiveKey=\"members\">\n          <TabPane tab=\"团队成员\" key=\"members\">\n            <Card\n              title=\"团队成员列表\"\n              extra={\n                hasManagePermission && (\n                  <Button\n                    type=\"primary\"\n                    icon={<PlusOutlined />}\n                    onClick={() => setInviteModalVisible(true)}\n                  >\n                    邀请成员\n                  </Button>\n                )\n              }\n            >\n              <ProTable\n                columns={memberColumns}\n                dataSource={members}\n                rowKey=\"id\"\n                loading={loading}\n                search={false}\n                pagination={{\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  showTotal: (total) => `共 ${total} 名成员`,\n                  pageSize: 10,\n                }}\n                options={{\n                  reload: () => fetchMembers(),\n                  setting: false,\n                  density: false,\n                }}\n                size=\"small\"\n              />\n            </Card>\n          </TabPane>\n          \n          <TabPane tab=\"邀请记录\" key=\"invitations\">\n            <Card title=\"邀请记录\">\n              <ProTable\n                columns={invitationColumns}\n                dataSource={invitations}\n                rowKey=\"id\"\n                loading={loading}\n                search={false}\n                pagination={{\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  showTotal: (total) => `共 ${total} 条邀请`,\n                  pageSize: 10,\n                }}\n                options={{\n                  reload: () => fetchInvitations(),\n                  setting: false,\n                  density: false,\n                }}\n                size=\"small\"\n              />\n            </Card>\n          </TabPane>\n          \n          {hasManagePermission && (\n            <TabPane tab=\"团队设置\" key=\"settings\">\n              <Card title=\"团队设置\">\n                <Space direction=\"vertical\" style={{ width: '100%' }} size=\"large\">\n                  <div>\n                    <Title level={5}>基本信息</Title>\n                    <Space>\n                      <Button\n                        icon={<EditOutlined />}\n                        onClick={() => {\n                          form.setFieldsValue({\n                            name: team.name,\n                            description: team.description\n                          });\n                          setEditModalVisible(true);\n                        }}\n                      >\n                        编辑团队信息\n                      </Button>\n                    </Space>\n                  </div>\n                  \n                  <div>\n                    <Title level={5} type=\"danger\">危险操作</Title>\n                    <Alert\n                      message=\"删除团队\"\n                      description=\"删除团队后，所有团队数据将无法恢复，请谨慎操作。\"\n                      type=\"warning\"\n                      showIcon\n                      action={\n                        <Button\n                          danger\n                          icon={<DeleteOutlined />}\n                          onClick={() => setDeleteModalVisible(true)}\n                        >\n                          删除团队\n                        </Button>\n                      }\n                    />\n                  </div>\n                </Space>\n              </Card>\n            </TabPane>\n          )}\n        </Tabs>\n      </Modal>\n\n      {/* 邀请成员模态框 */}\n      <Modal\n        title=\"邀请成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[{ required: true, message: '请输入邮箱地址' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入邮箱地址，多个邮箱用逗号分隔\"\n            />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"message\"\n            label=\"邀请消息（可选）\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"输入邀请消息...\"\n            />\n          </Form.Item>\n          \n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                发送邀请\n              </Button>\n              <Button onClick={() => {\n                setInviteModalVisible(false);\n                inviteForm.resetFields();\n              }}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 编辑团队信息模态框 */}\n      <Modal\n        title=\"编辑团队信息\"\n        open={editModalVisible}\n        onCancel={() => {\n          setEditModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleUpdateTeam}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"团队名称\"\n            rules={[{ required: true, message: '请输入团队名称' }]}\n          >\n            <Input placeholder=\"请输入团队名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label=\"团队描述\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入团队描述\"\n            />\n          </Form.Item>\n          \n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" loading={updating}>\n                保存\n              </Button>\n              <Button onClick={() => {\n                setEditModalVisible(false);\n                form.resetFields();\n              }}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 删除团队确认模态框 */}\n      <Modal\n        title=\"删除团队\"\n        open={deleteModalVisible}\n        onCancel={() => {\n          setDeleteModalVisible(false);\n          setDeleteConfirmText('');\n        }}\n        footer={[\n          <Button\n            key=\"cancel\"\n            onClick={() => {\n              setDeleteModalVisible(false);\n              setDeleteConfirmText('');\n            }}\n          >\n            取消\n          </Button>,\n          <Button\n            key=\"delete\"\n            type=\"primary\"\n            danger\n            loading={deleting}\n            disabled={deleteConfirmText !== team.name}\n            onClick={handleDeleteTeam}\n          >\n            确认删除\n          </Button>,\n        ]}\n      >\n        <Alert\n          message=\"警告\"\n          description=\"删除团队是不可逆的操作，将会删除所有团队数据。\"\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <div>\n          <Text>请输入团队名称 <Text code>{team.name}</Text> 来确认删除：</Text>\n          <Input\n            value={deleteConfirmText}\n            onChange={(e) => setDeleteConfirmText(e.target.value)}\n            placeholder={`请输入 ${team.name}`}\n            style={{ marginTop: 8 }}\n          />\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default TeamManagementModal;\n", "import { useModel } from '@umijs/max';\nimport { Col, Row, Spin } from 'antd';\nimport { ProCard } from '@ant-design/pro-components';\nimport React from 'react';\nimport UserFloatButton from '@/components/FloatButton';\nimport TeamListCard from './TeamListCard';\nimport TodoManagement from './TodoManagement';\nimport PersonalInfo from './PersonalInfo';\nimport DataOverview from './DataOverview';\n\n/**\n * 个人中心页面组件\n *\n * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。\n * 是用户进行个人设置和团队操作的主要入口页面。\n *\n * 页面功能：\n * 1. 用户个人信息展示和编辑\n * 2. 团队列表显示和团队切换\n * 3. 个人待办事项管理\n * 4. 全局浮动操作按钮\n *\n * 页面结构：\n * - 左列：个人信息、团队列表（响应式布局）\n * - 右列：待办事项管理（响应式布局）\n * - 数据概览：独立的水平卡片组件，位于个人信息下方\n * - 浮动：全局操作按钮\n *\n * 权限控制：\n * - 需要用户登录才能访问\n * - 自动检查登录状态并重定向\n * - 支持登录状态变化的实时响应\n *\n * 响应式设计：\n * - 移动端：垂直堆叠布局\n * - 桌面端：左右分栏布局\n * - 自适应不同屏幕尺寸\n */\nconst PersonalCenterPage: React.FC = () => {\n  /**\n   * 全局状态管理\n   *\n   * 从UmiJS全局状态中获取用户信息和加载状态：\n   * - loading: 全局状态的加载状态\n   */\n  const { loading } = useModel('@@initialState');\n\n\n\n  /**\n   * 加载状态处理\n   *\n   * 当全局状态正在初始化时，显示加载界面。\n   * 这确保了用户在状态加载完成前看到友好的加载提示。\n   */\n  if (loading) {\n    return (\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}\n      >\n        <Spin size=\"large\" />\n        <div style={{ marginLeft: 16 }}>正在加载用户信息...</div>\n      </div>\n    );\n  }\n\n  /**\n   * 登录状态检查已由应用级路由守卫处理\n   *\n   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。\n   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了\n   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。\n   *\n   * 这样可以避免登录成功后的状态更新时序问题，确保用户\n   * 一次登录成功后能够正常访问个人中心页面。\n   */\n\n  return (\n    <>\n      {/* 页面主容器 */}\n      <div\n        style={{\n          minHeight: '100vh',\n          background: '#f5f8ff', // 浅蓝色背景，营造清新的视觉效果\n          padding: '12px 12px 24px 12px', // 移动端优化：减少左右边距，增加底部边距\n        }}\n      >\n        {/*\n         * 主内容卡片容器\n         *\n         * 使用Card组件作为主要内容的容器，提供：\n         * 1. 统一的视觉边界和阴影效果\n         * 2. 响应式的内边距设置\n         * 3. 圆角设计提升视觉体验\n         * 4. 全高度布局适配不同屏幕\n         */}\n        <ProCard\n          style={{\n            width: '100%',\n            minHeight: 'calc(100vh - 48px)', // 减去外层padding的高度\n            borderRadius: '12px', // 圆角设计\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 轻微阴影效果\n          }}\n          bodyStyle={{\n            padding: '24px', // 内容区域的内边距\n          }}\n        >\n          {/*\n           * 响应式两列布局\n           *\n           * 使用Ant Design的Row/Col组件实现响应式两列布局：\n           * - 移动端：垂直堆叠，所有组件占满宽度\n           * - 桌面端：左列包含个人信息、数据概览、团队列表；右列包含待办事项\n           * - gutter: 组件间距设置\n           * - margin: 0: 避免Row组件的默认负边距影响布局\n           */}\n          <Row gutter={[24, 12]} style={{ margin: 0 }}>\n            {/*\n             * 左列：个人信息区域\n             *\n             * 包含以下组件（按顺序）：\n             * 1. 个人信息部分（用户详情、姓名、登录信息等）\n             * 2. 数据概览部分（车辆、人员、预警、告警统计）\n             * 3. 团队列表部分（用户所属团队列表）\n             *\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据左半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              {/* 个人信息部分（包含用户详情、姓名、登录信息） */}\n              <PersonalInfo />\n\n              {/* 数据概览部分 - 移动到左列 */}\n              <DataOverview />\n\n              {/* 团队列表部分（用户所属团队列表） */}\n              <TeamListCard />\n            </Col>\n\n            {/*\n             * 右列：待办事项管理区域\n             *\n             * 个人待办事项的管理界面，支持添加、编辑、删除待办事项。\n             * 响应式布局：\n             * - 移动端(xs-md)：全宽显示\n             * - 桌面端(lg+)：占据右半部分\n             */}\n            <Col\n              xs={24}\n              sm={24}\n              md={24}\n              lg={12}\n              xl={12}\n              xxl={12}\n            >\n              <TodoManagement />\n            </Col>\n          </Row>\n        </ProCard>\n      </div>\n\n      {/*\n       * 全局浮动操作按钮\n       *\n       * 提供快速访问常用功能的浮动按钮，如：\n       * - 快速创建团队\n       * - 用户设置\n       * - 帮助信息\n       *\n       * 位置固定在页面右下角，不受页面滚动影响。\n       */}\n      <UserFloatButton />\n\n\n\n\n    </>\n  );\n};\n\nexport default PersonalCenterPage;\n", "/**\n * TODO服务\n */\n\nimport type {\n  CreateTodoRequest,\n  TodoResponse,\n  TodoStatsResponse,\n  UpdateTodoRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\nexport class TodoService {\n  /**\n   * 获取用户的TODO列表\n   */\n  static async getUserTodos(): Promise<TodoResponse[]> {\n    const response = await apiRequest.get<TodoResponse[]>('/todos');\n    return response.data;\n  }\n\n  /**\n   * 创建TODO\n   */\n  static async createTodo(request: CreateTodoRequest): Promise<TodoResponse> {\n    const response = await apiRequest.post<TodoResponse>('/todos', request);\n    return response.data;\n  }\n\n  /**\n   * 更新TODO\n   */\n  static async updateTodo(\n    id: number,\n    request: UpdateTodoRequest,\n  ): Promise<TodoResponse> {\n    const response = await apiRequest.put<TodoResponse>(\n      `/todos/${id}`,\n      request,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除TODO\n   */\n  static async deleteTodo(id: number): Promise<void> {\n    await apiRequest.delete(`/todos/${id}`);\n  }\n\n  /**\n   * 获取TODO统计信息\n   */\n  static async getTodoStats(): Promise<TodoStatsResponse> {\n    const response = await apiRequest.get<TodoStatsResponse>('/todos/stats');\n    return response.data;\n  }\n}\n", "/**\n * 团队选择状态管理工具函数\n * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态\n */\n\n// 团队选择历史的本地存储键\nconst TEAM_SELECTION_KEY = 'user_team_selection_history';\n\n/**\n * 获取用户的团队选择历史\n * @param userId 用户ID\n * @returns 用户选择过的团队ID集合\n */\nexport const getUserTeamSelectionHistory = (userId: number): Set<number> => {\n  try {\n    const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    if (history) {\n      return new Set(JSON.parse(history));\n    }\n  } catch (error) {\n    console.error('获取团队选择历史失败:', error);\n  }\n  return new Set();\n};\n\n/**\n * 记录用户选择了某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n */\nexport const recordTeamSelection = (userId: number, teamId: number): void => {\n  try {\n    const history = getUserTeamSelectionHistory(userId);\n    history.add(teamId);\n    localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([...history]));\n    console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);\n  } catch (error) {\n    console.error('记录团队选择历史失败:', error);\n  }\n};\n\n/**\n * 检查用户是否曾经选择过某个团队\n * @param userId 用户ID\n * @param teamId 团队ID\n * @returns 是否曾经选择过该团队\n */\nexport const hasUserSelectedTeam = (userId: number, teamId: number): boolean => {\n  const history = getUserTeamSelectionHistory(userId);\n  return history.has(teamId);\n};\n\n/**\n * 清除用户的团队选择历史（用于注销等场景）\n * @param userId 用户ID\n */\nexport const clearUserTeamSelectionHistory = (userId: number): void => {\n  try {\n    localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);\n    console.log(`清除用户${userId}的团队选择历史`);\n  } catch (error) {\n    console.error('清除团队选择历史失败:', error);\n  }\n};\n\n/**\n * 获取所有用户的团队选择历史键（用于调试）\n * @returns 所有相关的localStorage键\n */\nexport const getAllTeamSelectionKeys = (): string[] => {\n  const keys: string[] = [];\n  for (let i = 0; i < localStorage.length; i++) {\n    const key = localStorage.key(i);\n    if (key && key.startsWith(TEAM_SELECTION_KEY)) {\n      keys.push(key);\n    }\n  }\n  return keys;\n};\n"], "names": [], "mappings": ";;;AAAA;;CAEC;;;;4BA0ED;;;eAAA;;;;;;;uEAxEkB;6BACE;8BAOb;4BAC0B;;;;;;;;;AAOjC;;CAEC,GACD,MAAM,4BAA6D,CAAC,EAClE,MAAM,EACN,YAAY,KAAK,EAClB;IACC,iBAAiB;IACjB,IAAI,aAAa,WAAW,qBAAgB,CAAC,OAAO,EAClD,qBACE,2BAAC,SAAG;QAAC,oBAAM,2BAAC,gCAAyB;;;;;QAAK,OAAM;kBAAS;;;;;;IAM7D,OAAQ;QACN,KAAK,qBAAgB,CAAC,OAAO;YAC3B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,0BAAmB;;;;;gBAAK,OAAM;0BAAO;;;;;;QAIrD,KAAK,qBAAgB,CAAC,QAAQ;YAC5B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,0BAAmB;;;;;gBAAK,OAAM;0BAAQ;;;;;;QAItD,KAAK,qBAAgB,CAAC,QAAQ;YAC5B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,0BAAmB;;;;;gBAAK,OAAM;0BAAM;;;;;;QAIpD,KAAK,qBAAgB,CAAC,OAAO;YAC3B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,gCAAyB;;;;;gBAAK,OAAM;0BAAS;;;;;;QAI7D,KAAK,qBAAgB,CAAC,SAAS;YAC7B,qBACE,2BAAC,SAAG;gBAAC,oBAAM,2BAAC,mBAAY;;;;;gBAAK,OAAM;0BAAU;;;;;;QAIjD;YACE,qBACE,2BAAC,SAAG;gBAAC,OAAM;0BAAU;;;;;;IAI3B;AACF;KAnDM;IAqDN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCgQf;;;eAAA;;;;;;8BAtUO;6BAOA;sCACiB;0DACmB;6BACf;;;;;;;;;;AAuB5B,MAAM,eAAyB;;IAE7B,MAAM,aAAa;QACjB,MAAM;YACJ,cAAc;YACd,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,iBAAiB;QACnB;QACA,SAAS;QAGT;QACA,WAAW;QAGX;QACA,SAAS;QAGT;QACA,OAAO;QAGP;IACF;IAIA,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAA4B;QAC5E,UAAU;QACV,WAAW;QACX,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAAC;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAgB;IAG5D,IAAA,gBAAS,EAAC;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,oBAAoB;gBACpD,iBAAiB;gBACjB,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,cAAc;YAChB,SAAU;gBACR,gBAAgB;YAClB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OACE,2BAAC,sBAAO;QACN,OACE,2BAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,YAAY;gBAAU,KAAK;YAAE;;gBAC1D,2BAAC,uBAAgB;oBAAC,OAAO;wBAAE,UAAU;wBAAI,OAAO;oBAAU;;;;;;gBAC1D,2BAAC;8BAAK;;;;;;;;;;;;QAGV,OAAO;YACL,cAAc;YACd,cAAc;YACd,QAAQ;QACV;QACA,WAAW;YACT,cAAc;YACd,eAAe;QACjB;QACA,WAAW;YACT,SAAS;QACX;kBAEC,aACC,2BAAC,WAAK;YACJ,SAAQ;YACR,aAAa;YACb,MAAK;YACL,QAAQ;YACR,OAAO;gBACL,cAAc;YAChB;;;;;mBAGF,2BAAC,UAAI;YAAC,UAAU;sBAEd,2BAAC,SAAG;gBAAC,QAAQ;oBAAC;oBAAI;iBAAG;;oBAEnB,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,2BAAC,UAAI;4BACH,OAAO;gCACL,GAAG,WAAW,IAAI;gCAClB,GAAG,WAAW,OAAO;4BACvB;4BACA,QAAQ;gCACN,MAAM;oCACJ,SAAS;oCACT,WAAW;gCACb;4BACF;;gCAGA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,2BAAC,kBAAW;wCACV,OAAO;4CACL,UAAU;4CACV,OAAO;4CACP,cAAc;wCAChB;;;;;;;;;;;gCAGJ,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,QAAQ;;;;;;gCAEzB,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,SAAS;oCACX;8CACD;;;;;;;;;;;;;;;;;oBAOL,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,2BAAC,UAAI;4BACH,OAAO;gCACL,GAAG,WAAW,IAAI;gCAClB,GAAG,WAAW,SAAS;4BACzB;4BACA,QAAQ;gCACN,MAAM;oCACJ,SAAS;oCACT,WAAW;gCACb;4BACF;;gCAGA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,2BAAC,2BAAoB;wCACnB,OAAO;4CACL,UAAU;4CACV,OAAO;4CACP,cAAc;wCAChB;;;;;;;;;;;gCAGJ,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,SAAS;;;;;;gCAE1B,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,SAAS;oCACX;8CACD;;;;;;;;;;;;;;;;;oBAOL,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,2BAAC,UAAI;4BACH,OAAO;gCACL,GAAG,WAAW,IAAI;gCAClB,GAAG,WAAW,OAAO;4BACvB;4BACA,QAAQ;gCACN,MAAM;oCACJ,SAAS;oCACT,WAAW;gCACb;4BACF;;gCAGA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,2BAAC,gCAAyB;wCACxB,OAAO;4CACL,UAAU;4CACV,OAAO;4CACP,cAAc;wCAChB;;;;;;;;;;;gCAGJ,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,QAAQ;;;;;;gCAEzB,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,SAAS;oCACX;8CACD;;;;;;;;;;;;;;;;;oBAOL,2BAAC,SAAG;wBAAC,IAAI;wBAAI,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;kCACpC,2BAAC,UAAI;4BACH,OAAO;gCACL,GAAG,WAAW,IAAI;gCAClB,GAAG,WAAW,KAAK;4BACrB;4BACA,QAAQ;gCACN,MAAM;oCACJ,SAAS;oCACT,WAAW;gCACb;4BACF;;gCAGA,2BAAC;oCAAI,OAAO;wCAAE,cAAc;oCAAG;8CAC7B,2BAAC,oBAAa;wCACZ,OAAO;4CACL,UAAU;4CACV,OAAO;4CACP,cAAc;wCAChB;;;;;;;;;;;gCAGJ,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,cAAc;oCAChB;8CAEC,cAAc,MAAM;;;;;;gCAEvB,2BAAC;oCACC,OAAO;wCACL,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,SAAS;oCACX;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAnSM;KAAA;IAqSN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCrJf;;;eAAA;;;;;;;8BApLO;6BAMA;sCACiB;0DAEmB;6BACf;wEAEK;mEACL;;;;;;;;;;AAE5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAiBlC,MAAM,eAAyB;;IAI7B,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAA4B;QAClE,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW;QACX,cAAc;QACd,eAAe;QACf,eAAe;QACf,WAAW;QACX,QAAQ;IACV;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAGlE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,IAAA,eAAQ,EAAC;IAGjE,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,aAAa,MAAM,iBAAW,CAAC,oBAAoB;gBACzD,YAAY;gBACZ,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,eAAe;gBAC7B,iBAAiB;YACnB,SAAU;gBACR,mBAAmB;YACrB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OACE;;YACE,2BAAC,sBAAO;gBACN,OACE,2BAAC,WAAK;oBAAC,MAAM;;wBACX,2BAAC,wBAAe;4BAAC,UAAU;sCACzB,2BAAC,6BAAsB;gCACrB,OAAO;oCACL,UAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,YAAY;oCACZ,SAAS;oCACT,cAAc;oCACd,YAAY;gCACd;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oCAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oCACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCACpC;gCACA,cAAc,CAAC;oCACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oCAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;oCACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCACpC;;;;;;;;;;;wBAGJ,2BAAC,sBAAe;4BACd,OAAO;gCACL,UAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,YAAY;gCACZ,SAAS;gCACT,cAAc;gCACd,YAAY;4BACd;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gCAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gCACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BACpC;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gCAC9B,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;gCACnC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BACpC;4BACA,SAAS,IAAM,wBAAwB;;;;;;;;;;;;gBAI7C,OAAO;oBACL,cAAc;oBACd,cAAc;oBACd,QAAQ;gBACV;0BAED,gBACC,2BAAC,WAAK;oBACJ,SAAQ;oBACR,aAAa;oBACb,MAAK;oBACL,QAAQ;oBACR,OAAO;wBACL,cAAc;wBACd,QAAQ;oBACV;;;;;2BAGF,2BAAC,UAAI;oBAAC,UAAU;8BAEd,2BAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,gBAAgB;4BAChB,YAAY;4BACZ,SAAS;wBACX;kCAEE,2BAAC,gBAAU,CAAC,KAAK;4BACf,OAAO;4BACP,OAAO;gCACL,QAAQ;gCACR,UAAU;gCACV,OAAO;4BACT;;gCACD;gCACK,SAAS,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;YAQ7B,2BAAC,6BAAoB;gBACnB,SAAS;gBACT,UAAU,IAAM,wBAAwB;gBACxC,UAAU;gBACV,WAAW;oBAET,QAAQ,GAAG,CAAC;gBACd;;;;;;;;AAIR;GAlJM;KAAA;IAoJN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC+7Bf;;;eAAA;;;;;;;8BA1mCO;4BAC2B;6BAa3B;sCAC0B;0DACgC;iCACrC;6BACA;mCAMrB;2CACkD;uEACzB;;;;;;;;;;AAEhC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAMlC,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EhB,CAAC;AAiCD,MAAM,eAAyB;;IAU7B,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAgB;IAStE,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAS;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAS;IAGvE,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,IAAA,eAAQ,EAAC;IAC7E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,eAAQ,EAAC;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,eAAQ,EAA4B;IAkB5E,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IACnD,MAAM,cAAc,yBAAA,mCAAA,aAAc,WAAW;IAe7C,MAAM,qBAAqB,IAAA,qCAAyB;IACpD,MAAM,gBAAgB,IAAA,qCAAyB;IAC/C,MAAM,iBAAiB,IAAA,iCAAqB;IAO5C,MAAM,qBAAqB,CAAC,CAC1B,CAAA,kBACA,sBACA,eACA,YAAY,EAAE,KAAK,sBACnB,iBACA,IAAA,uCAAmB,EAAC,eAAe,mBAAkB;IAIvD,MAAM,sBAAsB,qBAAqB,qBAAqB;IAGtE,QAAQ,GAAG,CAAC,sBAAsB;QAChC,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;QAC5B;QACA;QACA;QACA;QACA;QACA,4BAA4B,iBAAiB,qBAAqB,IAAA,uCAAmB,EAAC,eAAe,sBAAsB;QAC3H,yBAAyB,CAAC,EAAC,yBAAA,mCAAA,aAAc,WAAW;IACtD;IAGA,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,YAAY,MAAM,iBAAW,CAAC,qBAAqB;YACzD,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,gBAAS,EAAC;QAER,IAAI,yBAAA,mCAAA,aAAc,WAAW,EAC3B;IAEJ,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAG9B,IAAA,gBAAS,EAAC;QAER,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAAE;YAC9B,SAAS,EAAE;YACX,SAAS;YACT,WAAW;YACX,mBAAmB;QACrB;IACF,GAAG;QAAC,yBAAA,mCAAA,aAAc,WAAW;KAAC;IAG9B,IAAA,gBAAS,EAAC;QACR,QAAQ,GAAG,CAAC,aAAa;YACvB,WAAW,EAAE,wBAAA,kCAAA,YAAa,EAAE;YAC5B;YACA;QACF;IACF,GAAG;QAAC,wBAAA,kCAAA,YAAa,EAAE;QAAE;QAAqB;KAAmB;IAQ7D,IAAA,gBAAS,EAAC;QACR,MAAM,QAAQ,WAAW;YACvB,uBAAuB;QACzB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAW;IAQf,MAAM,gBAAgB,IAAA,cAAO,EAAC;QAC5B,IAAI,CAAC,oBAAoB,IAAI,IAC3B,OAAO;QAGT,MAAM,cAAc,oBAAoB,WAAW,GAAG,IAAI;QAC1D,OAAO,MAAM,MAAM,CAAC,CAAC,OACnB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAErC,GAAG;QAAC;QAAO;KAAoB;IAW/B,MAAM,qBAAqB,IAAA,kBAAW,EAAC,CAAC;QACtC,cAAc,EAAE,MAAM,CAAC,KAAK;IAC9B,GAAG,EAAE;IAKL,MAAM,sBAAsB,IAAA,kBAAW,EAAC,CAAC;QACvC,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,cAAc;YACd,EAAE,aAAa,CAAC,IAAI;QACtB;IACF,GAAG,EAAE;IAKqB,IAAA,kBAAW,EAAC;QACpC,cAAc;IAChB,GAAG,EAAE;IA8BL,MAAM,mBAAmB,OAAO,QAAgB;QAO9C,IAAI,EAAC,yBAAA,mCAAA,aAAc,WAAW,GAC5B;QAGF,IAAI;YASF,mBAAmB;YAQnB,IAAI,WAAW,qBAAqB;gBAClC,YAAO,CAAC,IAAI,CAAC;gBACb;YACF;YAUA,MAAM,WAAW,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE;YAAO;YAUvD,IACE,SAAS,oBAAoB,IAC7B,SAAS,IAAI,IACb,SAAS,IAAI,CAAC,EAAE,KAAK,QACrB;gBASA,IAAI,eACF,IAAA,uCAAmB,EAAC,eAAe;gBAerC,IACE,CAAA,yBAAA,mCAAA,aAAc,aAAa,MAC3B,yBAAA,mCAAA,aAAc,aAAa,KAC3B,iBAGA,QAAQ,GAAG,CAAC;oBACV,aAAa,aAAa;oBAC1B,aAAa,aAAa;iBAC3B,EACE,IAAI,CAAC,CAAC,CAAC,aAAa,YAAY;oBAE/B,IAAI,eAAe,YAAY,EAAE,KAAK,QACpC,gBAAgB;wBACd,GAAG,YAAY;wBACf;wBACA;oBACF;gBAEJ,GACC,KAAK,CAAC,CAAC;oBACN,QAAQ,KAAK,CAAC,uBAAuB;gBAEvC;gBASJ,YAAO,CAAC,IAAI,CAAC;YACf;QASF,EAAE,OAAO,OAAY,CAerB,SAAU;YAOR,mBAAmB;QACrB;IACF;IAKA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YAEF,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE,QAAQ,KAAK,EAAE;YAAC;YAC/C,gBAAgB;YAChB,8BAA8B;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAKA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,yBAAyB;IAC3B;IAKA,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;QAEnB,IAAI;YAEF,MAAM,qBAAW,CAAC,UAAU,CAAC;gBAAE,QAAQ,aAAa,EAAE;YAAC;YAGvD,MAAM,iBAAW,CAAC,SAAS;YAG3B,MAAM;YAGN,IAAI,iBACF,MAAM,gBAAgB,CAAC,YAAe,CAAA;oBACpC,GAAG,SAAS;oBACZ,aAAa;gBACf,CAAA;YAGF,aAAO,CAAC,OAAO,CAAC;YAChB,yBAAyB;YACzB,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAIA,OACE;;YAIE,2BAAC,sBAAO;gBACN,OAAM;gBACN,OAAO;oBACL,cAAc;oBACd,cAAc;oBACd,QAAQ;gBACV;;oBAGC,CAAA,yBAAA,mCAAA,aAAc,WAAW,KAAI,MAAM,MAAM,GAAG,KAC3C,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;kCAC7B,2BAAC,SAAG;4BAAC,QAAQ;gCAAC;gCAAI;6BAAE;sCAClB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;0CACvC,2BAAC,WAAK,CAAC,MAAM;oCACX,aAAY;oCACZ,UAAU;oCACV,QAAQ,2BAAC,qBAAc;;;;;oCACvB,OAAO;oCACP,UAAU;oCACV,WAAW;oCACX,OAAO;wCAAE,OAAO;oCAAO;oCACvB,MAAK;;;;;;;;;;;;;;;;;;;;;oBAMd,QACC,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAa;wBACb,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;+BAG5B,2BAAC,UAAI;wBAAC,UAAU;kCACb,EAAC,yBAAA,mCAAA,aAAc,WAAW,IACzB,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAY;sCACtD,2BAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;mCAEvB,MAAM,MAAM,KAAK,KAAK,CAAC,UACzB,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAY;sCACtD,2BAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;mCAEvB,cAAc,MAAM,KAAK,KAAK,oBAAoB,IAAI,KACxD,2BAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,SAAS;4BAAY;;gCACtD,2BAAC;oCAAK,MAAK;8CAAY;;;;;;gCACvB,2BAAC;oCAAI,OAAO;wCAAE,WAAW;oCAAE;8CACzB,2BAAC;wCAAK,MAAK;wCAAY,OAAO;4CAAE,UAAU;wCAAG;kDAAG;;;;;;;;;;;;;;;;mCAMpD,2BAAC,sBAAO;4BACN,YAAY;4BACZ,OAAO;4BACP,YAAW;4BACX,YAAY,CAAC;oCAkUQ,aAiCA,cAiCA,cAiCA;uCApanB,2BAAC,sBAAO;oCACN,WAAU;oCACV,OAAO;wCACL,cAAc;wCACd,iBACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;wCACN,cAAc;wCACd,OAAO;wCACP,YAAY,CAAC,UAAU,EAAE,KAAK,SAAS,GAAG,YAAY,UAAU,CAAC;wCACjE,QACE,wBAAwB,KAAK,EAAE,GAC3B,sBACA;wCAEN,UAAU;oCACZ;8CAGE,2BAAC,SAAG;wCACF,QAAQ;4CAAC;4CAAG;yCAAE;wCACd,OAAM;wCACN,OAAO;4CAAE,OAAO;wCAAO;;4CAGvB,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,2BAAC,UAAI;oDAAC,QAAQ;oDAAC,KAAK;oDAAG,WAAU;;wDAAiB;wDAEhD,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAG,MAAK;;gEAAO;gEACvC,2BAAC;oEACC,OAAO;wEACL,QAAQ;wEACR,SAAS;wEACT,cAAc;wEACd,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;oEACA,SAAS,IACP,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;oEAErC,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;oEACJ;oEACA,cAAc,CAAC;wEACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAC9B;oEACJ;;wEAEA,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;gFACN,YAAY;4EACd;sFAEC,KAAK,IAAI;;;;;;wEAEZ,2BAAC,oBAAa;4EACZ,OAAO;gFACL,UAAU;gFACV,OACE,wBAAwB,KAAK,EAAE,GAC3B,YACA;gFACN,eAAe;gFACf,SAAS;gFACT,YAAY;4EACd;;;;;;;;;;;;gEAKH,wBAAwB,KAAK,EAAE,IAC9B,2BAAC;oEACC,OAAO;wEACL,YAAY;wEACZ,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;oEACd;8EACD;;;;;;gEAOF,oBAAoB,KAAK,EAAE,IAC1B,2BAAC,UAAI;oEAAC,OAAM;oEAAS,KAAK;;wEACxB,2BAAC,UAAI;4EAAC,MAAK;;;;;;wEACX,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;;gFAAG;gFAAa;;;;;;;;;;;;;;;;;;;wDAQjE,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAI,MAAK;4DAAO,WAAU;;gEAAiB;gEACnE,2BAAC,aAAO;oEACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;8EAEpE,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;4EAAG;4EAC3B,2BAAC,0BAAmB;gFAClB,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;4EAE1C,2BAAC;gFACC,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFACzC;oFACM,IAAI,KACP,KAAK,SAAS,EACd,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;gEAM1B,KAAK,UAAU,IACd,2BAAC,aAAO;oEACN,OAAO,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,UAAU,EAAE,cAAc,CAAC,SAAS,CAAC;8EAErE,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;4EAAG;4EAC3B,2BAAC,mBAAY;gFACX,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;4EAE1C,2BAAC;gFACC,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFACzC;oFACM,IAAI,KACP,KAAK,UAAU,EACf,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;gEAM7B,2BAAC,aAAO;oEACN,OAAO,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;8EAEnC,2BAAC,UAAI;wEAAC,OAAM;wEAAS,KAAK;;4EAAG;4EAC3B,2BAAC,mBAAY;gFACX,OAAO;oFAAE,OAAO;oFAAW,UAAU;gFAAG;;;;;;4EAE1C,2BAAC;gFACC,OAAO;oFAAE,UAAU;oFAAI,OAAO;gFAAU;;oFAEvC,KAAK,WAAW;oFAAC;;;;;;;;;;;;;;;;;;;;;;;;wDAO1B,2BAAC,UAAI;4DAAC,OAAM;4DAAS,KAAK;4DAAG,MAAK;4DAAO,WAAU;;gEAAqB;gEAEtE,2BAAC;oEACC,OAAO;wEACL,YAAY,KAAK,SAAS,GAAG,YAAY;wEACzC,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;8EAEC,KAAK,SAAS,GACb;;4EACE,2BAAC,mBAAY;gFAAC,OAAO;oFAAE,UAAU;gFAAG;;;;;;4EAAK;;uFAI3C;;4EACE,2BAAC,mBAAY;gFAAC,OAAO;oFAAE,UAAU;gFAAG;;;;;;4EAAK;;;;;;;;gEAO/C,2BAAC;oEACC,OAAO;wEACL,YAAY,KAAK,QAAQ,GAAG,YAAY;wEACxC,OAAO;wEACP,SAAS;wEACT,cAAc;wEACd,UAAU;wEACV,YAAY;wEACZ,SAAS;wEACT,YAAY;wEACZ,KAAK;oEACP;8EAEC,KAAK,QAAQ,GACZ;;4EACE,2BAAC,0BAAmB;gFAAC,OAAO;oFAAE,UAAU;gFAAG;;;;;;4EAAK;4EAAa;;uFAI/D;;4EACE,2BAAC,0BAAmB;gFAAC,OAAO;oFAAE,UAAU;gFAAG;;;;;;4EAAK;4EAAa;;;;;;;;gEAOlE,KAAK,SAAS,GACb,2BAAC,aAAO;oEAAC,OAAM;8EACb,2BAAC,YAAM;wEACL,MAAK;wEACL,MAAK;wEACL,MAAM,2BAAC,sBAAe;4EAAC,OAAO;gFAAE,UAAU;4EAAG;;;;;;wEAC7C,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,qBAAqB;wEACvB;wEACA,OAAO;4EACL,OAAO;4EACP,SAAS;4EACT,YAAY;4EACZ,gBAAgB;4EAChB,OAAO;4EACP,QAAQ;4EACR,SAAS;4EACT,cAAc;4EACd,QAAQ;4EACR,YAAY;4EACZ,YAAY;wEACd;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4EACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;wEACtC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4EACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;wEACtC;;;;;;;;;;6EAIJ,2BAAC,aAAO;oEAAC,OAAM;8EACb,2BAAC,YAAM;wEACL,MAAK;wEACL,MAAK;wEACL,MAAM,2BAAC,qBAAc;4EAAC,OAAO;gFAAE,UAAU;4EAAG;;;;;;wEAC5C,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,gBAAgB;wEAClB;wEACA,OAAO;4EACL,OAAO;4EACP,SAAS;4EACT,YAAY;4EACZ,gBAAgB;4EAChB,OAAO;4EACP,QAAQ;4EACR,SAAS;4EACT,cAAc;4EACd,QAAQ;4EACR,YAAY;4EACZ,YAAY;wEACd;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4EACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;wEACtC;wEACA,cAAc,CAAC;4EACb,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG;4EACnC,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;wEACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CASZ,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,2BAAC,SAAG;oDACF,QAAQ;wDAAC;wDAAG;qDAAE;oDACd,SAAS;wDAAE,IAAI;wDAAS,IAAI;oDAAM;oDAClC,OAAM;;wDAGN,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;wEAAG;wEACpC,2BAAC,kBAAW;4EACV,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;wEAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,cAAA,KAAK,KAAK,cAAV,kCAAA,YAAY,QAAQ,KAAI;;;;;;wEAE3B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;;gFAAG;gFAAe;;;;;;;;;;;;;;;;;;;;;;;wDAQnE,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;wEAAG;wEACpC,2BAAC,mBAAY;4EACX,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;wEAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,SAAS,KAAI;;;;;;wEAE5B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;;gFAAG;gFAAe;;;;;;;;;;;;;;;;;;;;;;;wDAQnE,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;wEAAG;wEACpC,2BAAC,gCAAyB;4EACxB,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;wEAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,QAAQ,KAAI;;;;;;wEAE3B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;;gFAAG;gFAAe;;;;;;;;;;;;;;;;;;;;;;;wDAQnE,2BAAC,SAAG;4DAAC,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;4DAAG,IAAI;sEACnC,2BAAC;gEACC,OAAO;oEACL,YAAY;oEACZ,QAAQ;oEACR,cAAc;oEACd,SAAS;oEACT,WAAW;oEACX,UAAU;gEACZ;0EAEA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;wEAAG;wEACpC,2BAAC,gCAAyB;4EACxB,OAAO;gFAAE,OAAO;gFAAW,UAAU;4EAAG;;;;;;wEAE1C,2BAAC;4EACC,MAAM;4EACN,OAAO;gFACL,UAAU;gFACV,OAAO;gFACP,YAAY;4EACd;sFAEC,EAAA,eAAA,KAAK,KAAK,cAAV,mCAAA,aAAY,OAAO,KAAI;;;;;;wEAE1B,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;4EAAO;;gFAAG;gFAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAoBzF,2BAAC,4BAAmB;gBAClB,SAAS;gBACT,UAAU;oBACR,8BAA8B;oBAC9B,gBAAgB;gBAClB;gBACA,MAAM;gBACN,WAAW;;;;;;YAIb,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,yBAAyB;oBACzB,gBAAgB;gBAClB;gBACA,QAAQ;oBACN,2BAAC,YAAM;wBAEL,SAAS;4BACP,yBAAyB;4BACzB,gBAAgB;wBAClB;kCACD;uBALK;;;;;oBAQN,2BAAC,YAAM;wBAEL,MAAK;wBACL,MAAM;wBACN,SAAS;kCACV;uBAJK;;;;;iBAOP;0BAED,2BAAC;oBAAI,OAAO;wBAAE,WAAW;wBAAU,SAAS;oBAAS;;wBACnD,2BAAC;;gCAAE;gCAAQ,2BAAC;8CAAQ,yBAAA,mCAAA,aAAc,IAAI;;;;;;gCAAU;;;;;;;wBAChD,2BAAC;4BAAE,OAAO;gCAAE,OAAO;4BAAU;sCAAG;;;;;;;;;;;;;;;;;;;AAO1C;GA19BM;;QA8CsC,aAAQ;;;KA9C9C;IA49BN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCzcf;;;eAAA;;;;;;8BArqBO;6BAiBA;sCACqC;wEACD;6BACf;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;AASxB,MAAM,iBAAgD;;IACpD,aAAa;IACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;QAC5D,mBAAmB;QACnB,qBAAqB;QACrB,kBAAkB;QAClB,YAAY;QACZ,gBAAgB;QAChB,sBAAsB;IACxB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;IAElD,WAAW;IACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;IAElE,QAAQ;IACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAE7C,WAAW;IACX,IAAA,gBAAS,EAAC;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;oBACrD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO,EAAE;gBACX;gBAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;oBACrD,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;wBACL,mBAAmB;wBACnB,qBAAqB;wBACrB,kBAAkB;wBAClB,YAAY;wBACZ,gBAAgB;wBAChB,sBAAsB;oBACxB;gBACF;gBAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAAC;oBAAc;iBAAa;gBAErE,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,iBAAiB;gBACjB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,wBAAwB,AAAC,CAAA,iBAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,CAAC;QAC1D,SAAS;QACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;QACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;QAE3D,WAAW;QACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;QAGT,OAAO;IACT;IAEA,WAAW;IACX,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAChD,IAAI,CAAC,MACH;YAGF,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;YAE1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;gBAAE,QAAQ;YAAU;YAErD,SAAS;YACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,IAAI;YAItD,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,IAAI,eAAe;gBACjB,WAAW;gBACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;oBAC9D,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;YAGhD,OAAO;gBACL,UAAU;gBACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;oBAC3C,OAAO,OAAO,IAAI;oBAClB,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,iBAAiB;oBAAC;uBAAY;iBAAc;YAC9C;YAEA,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;YAEA,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,SAAS,WAAW;QACtB,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,iBAAW,CAAC,UAAU,CAAC;YAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;YAE5D,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;gBAC5C,aAAa;YACf,EAAE,OAAO,YAAY;YACnB,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;QACd,iBAAiB;QACnB;IACF;IAEA,qBACE,2BAAC,sBAAO;QACN,OAAM;QACN,OAAO;YACL,cAAc;YACd,QAAQ;YACR,WAAW;QACb;QACA,WAAW;YACT,cAAc;YACd,eAAe;QACjB;QACA,WAAW;YACT,SAAS;QACX;;0BAGA,2BAAC;gBACC,OAAO;oBACL,cAAc;oBACd,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,QAAQ;gBACV;;kCAGA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,cAAc;wBAAG;;0CAE/C,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;0CACvC,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;oCAAI,MAAK;;sDAEjC,2BAAC;4CACC,OAAO;gDACL,YAAY;gDACZ,QAAQ;gDACR,cAAc;gDACd,SAAS;gDACT,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,UAAU;4CACZ;;8DAEA,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;;;;;;8DAEF,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;wDAAW,aAAa;oDAAE;8DAAG;;;;;;8DAGjE,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;wDAAK,OAAO;oDAAU;8DAC5D,UAAU,iBAAiB;;;;;;;;;;;;sDAKhC,2BAAC;4CACC,OAAO;gDACL,YAAY;gDACZ,QAAQ;gDACR,cAAc;gDACd,SAAS;gDACT,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,UAAU;4CACZ;;8DAEA,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;;;;;;8DAEF,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;wDAAW,aAAa;oDAAE;8DAAG;;;;;;8DAGjE,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;wDAAK,OAAO;oDAAU;8DAC5D,UAAU,mBAAmB;;;;;;;;;;;;sDAKlC,2BAAC;4CACC,OAAO;gDACL,YAAY;gDACZ,QAAQ;gDACR,cAAc;gDACd,SAAS;gDACT,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,UAAU;4CACZ;;8DAEA,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;;;;;;8DAEF,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;wDAAW,aAAa;oDAAE;8DAAG;;;;;;8DAGjE,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,YAAY;wDAAK,OAAO;oDAAU;8DAC5D,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAOnC,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;0CACtC,cAAA,2BAAC;oCACC,OAAO;wCACL,YAAY;wCACZ,QAAQ;wCACR,cAAc;wCAEd,QAAQ;wCACR,SAAS;wCACT,YAAY;wCACZ,gBAAgB;oCAClB;8CAEA,cAAA,2BAAC,aAAO;wCACN,OAAO,CAAC,KAAK,EAAE,UAAU,oBAAoB,CAAC,GAAG,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;kDAEtG,cAAA,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;8DACxB,2BAAC;oDAAK,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAU;8DAAG;;;;;;8DAGjD,2BAAC,cAAQ;oDACP,SAAS,UAAU,oBAAoB;oDACvC,MAAK;oDACL,OAAO;wDAAE,OAAO;oDAAG;oDACnB,aAAY;oDACZ,UAAU;;;;;;8DAEZ,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YAAY;wDACZ,OAAO;oDACT;;wDAEC,UAAU,oBAAoB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5C,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAE;;0CAClB,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAI,IAAI;0CACvC,cAAA,2BAAC,WAAK,CAAC,MAAM;oCACX,aAAY;oCACZ,UAAU;oCACV,sBAAQ,2BAAC,qBAAc;;;;;oCACvB,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,OAAO;wCAAE,OAAO;oCAAO;oCACvB,MAAK;;;;;;;;;;;0CAIT,2BAAC,SAAG;gCAAC,IAAI;gCAAI,IAAI;gCAAI,IAAI;gCAAG,IAAI;gCAAG,IAAI;0CACrC,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,mBAAY;;;;;oCACnB,SAAS;wCACP,iBAAiB;wCACjB,SAAS,WAAW;wCACpB,oBAAoB;oCACtB;oCACA,OAAO;wCACL,YAAY;wCACZ,aAAa;wCACb,WAAW;wCACX,YAAY;wCACZ,OAAO;oCACT;oCACA,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;0BAQP,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU,CAAC,MAAQ,aAAa;gBAChC,MAAK;gBACL,OAAO;oBAAE,cAAc;gBAAE;;kCAEzB,2BAAC;wBAAQ,KAAI;uBAAS;;;;;kCACtB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;kCACvB,2BAAC;wBAAQ,KAAI;uBAAU;;;;;;;;;;;YAIxB,sBACC,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAa;gBACb,MAAK;gBACL,QAAQ;gBACR,OAAO;oBAAE,cAAc;gBAAG;;;;;qCAG5B,2BAAC,UAAI;gBAAC,UAAU;;kCACd,2BAAC,sBAAO;wBACN,YAAY;wBACZ,YAAY,CAAC;4BACX,qBACE,2BAAC;gCACC,WAAU;gCACV,OAAO;oCACL,SAAS;oCACT,cAAc;oCACd,cAAc;oCACd,YAAY;oCACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;oCACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACT,CAAC;oCACF,WAAW;gCACb;0CAEA,cAAA,2BAAC,UAAI;oCAAC,OAAM;oCAAS,KAAK;oCAAI,OAAO;wCAAE,OAAO;oCAAO;;sDAEnD,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAM;;gDAClB,KAAK,MAAM,KAAK,kBACf,2BAAC,UAAI;oDACH,OAAM;oDACN,SAAQ;oDACR,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;oDACd;8DAEA,cAAA,2BAAC,oBAAa;wDACZ,OAAO;4DAAE,OAAO;4DAAQ,UAAU;wDAAG;;;;;;;;;;2EAIzC,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACP,CAAC;oDACJ;;;;;;8DAIJ,2BAAC;oDACC,OAAO;wDACL,OAAO;wDACP,QAAQ;wDACR,YAAY;wDACZ,WAAW;oDACb;;;;;;;;;;;;sDAKJ,2BAAC,UAAI;4CAAC,QAAQ;4CAAC,OAAO;gDAAE,MAAM;4CAAE;;8DAC9B,2BAAC;oDACC,OAAO;wDACL,UAAU;wDACV,YAAY,KAAK,QAAQ,KAAK,IAAI,MAAM;wDACxC,gBACE,KAAK,MAAM,KAAK,IAAI,iBAAiB;wDACvC,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;oDACzC;8DAEC,KAAK,KAAK;;;;;;8DAIb,2BAAC,WAAK;oDAAC,OAAM;oDAAS,MAAM;oDAAG,OAAO;wDAAE,WAAW;oDAAE;;sEACnD,2BAAC,uBAAgB;4DACf,OAAO;gEACL,UAAU;gEACV,OAAO;4DACT;;;;;;sEAEF,2BAAC;4DAAK,MAAK;4DAAY,OAAO;gEAAE,UAAU;4DAAG;;gEAAG;gEACzC;gEACJ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;sDAMnD,2BAAC,cAAQ;4CACP,SAAS;gDAAC;6CAAQ;4CAClB,MAAM;gDACJ,OAAO;oDACL;wDACE,KAAK;wDACL,OACE,KAAK,MAAM,KAAK,IAAI,UAAU;wDAChC,oBACE,2BAAC,oBAAa;4DACZ,OAAO;gEACL,OACE,KAAK,MAAM,KAAK,IAAI,YAAY;gEAClC,UAAU;4DACZ;;;;;;oDAGN;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBAAM,2BAAC,mBAAY;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;oDAChD;oDACA;wDACE,KAAK;wDACL,OAAO;wDACP,oBACE,2BAAC,qBAAc;4DAAC,OAAO;gEAAE,OAAO;4DAAU;;;;;;wDAE5C,QAAQ;oDACV;iDACD;gDACD,SAAS,CAAC,EAAE,GAAG,EAAE;oDACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;yDACzB,IAAI,QAAQ,QAAQ;wDACzB,iBAAiB,KAAK,EAAE;wDACxB,SAAS,cAAc,CAAC;4DACtB,MAAM,KAAK,KAAK;4DAChB,UAAU,KAAK,QAAQ;wDACzB;wDACA,oBAAoB;oDACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;gDAE5B;4CACF;sDAEA,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,oBAAM,2BAAC,mBAAY;;;;;gDACnB,OAAO;oDAAE,OAAO;oDAAI,QAAQ;gDAAG;;;;;;;;;;;;;;;;;;;;;;wBAM3C;;;;;;kCAIF,2BAAC,wBAAS;wBACR,OAAO,gBAAgB,WAAW;wBAClC,MAAM;wBACN,cAAc,CAAC;4BACb,oBAAoB;4BACpB,IAAI,CAAC,SAAS;gCACZ,iBAAiB;gCACjB,SAAS,WAAW;4BACtB;wBACF;wBACA,MAAM;wBACN,QAAO;wBACP,UAAU;wBACV,cAAa;wBACb,OAAO;wBACP,YAAY;4BACV,UAAU;4BACV,gBAAgB;4BAChB,cAAc;4BACd,UAAU;4BACV,aAAa;wBACf;wBACA,WAAW;4BACT,cAAc;gCACZ,YAAY,gBAAgB,SAAS;gCACrC,WAAW;4BACb;4BACA,mBAAmB;gCACjB,OAAO;oCACL,YAAY;oCACZ,aAAa;oCACb,WAAW;gCACb;gCACA,MAAM,8BAAgB,2BAAC,mBAAY;;;;2DAAM,2BAAC,mBAAY;;;;;4BACxD;4BACA,kBAAkB;gCAChB,OAAO;oCACL,aAAa;gCACf;4BACF;4BACA,SAAS;gCACP,oBAAoB;gCACpB,iBAAiB;gCACjB,SAAS,WAAW;4BACtB;wBACF;wBACA,UAAU;;0CAEV,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS;oCAAU;iCAAE;0CAE/C,cAAA,2BAAC,WAAK;oCACJ,aAAY;oCACZ,MAAK;oCACL,OAAO;wCAAE,cAAc;oCAAE;;;;;;;;;;;0CAI7B,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;gCACN,cAAc;gCACd,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS;oCAAS;iCAAE;0CAE9C,cAAA,2BAAC,YAAM;oCACL,MAAK;oCACL,SAAS;wCACP;4CAAE,OAAO;4CAAG,OAAO;wCAAO;wCAC1B;4CAAE,OAAO;4CAAG,OAAO;wCAAO;wCAC1B;4CAAE,OAAO;4CAAG,OAAO;wCAAO;qCAC3B;oCACD,OAAO;wCAAE,cAAc;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAloBM;;QAgBe,UAAI,CAAC;;;KAhBpB;IAooBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC/Wf;;;eAAA;;;;;;8BA9T4D;6BAUrD;wEACoC;;;;;;;;;;AAG3C,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AASlC;;;;;;;;;;;;;;;CAeC,GACD,MAAM,uBAA4D,CAAC,EACjE,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACT;;IACC,MAAM,CAAC,aAAa,GAAG,UAAI,CAAC,OAAO;IACnC,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,uBAAuB;IACvB,IAAA,gBAAS,EAAC;QACR,IAAI,WAAW,UACb,aAAa,cAAc,CAAC;YAC1B,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;YACrB,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;QAC7B;IAEJ,GAAG;QAAC;QAAS;QAAU;KAAa;IAEpC,WAAW;IACX,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,SAAS,MAAM,aAAa,cAAc;YAChD,QAAQ,GAAG,CAAC,WAAW;YAEvB,qBAAqB;YACrB,+CAA+C;YAE/C,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAA,wBAAA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,cAAc;YAC5C,QAAQ,GAAG,CAAC,SAAS;YAErB,mBAAmB;YACnB,wCAAwC;YAExC,aAAO,CAAC,OAAO,CAAC;YAChB,SAAS,WAAW;YACpB,sBAAA,wBAAA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,aAAa,WAAW;QACxB,SAAS,WAAW;QACpB,aAAa;QACb;IACF;IAEA,gBAAgB;IAChB,MAAM,WAAW;QACf,IAAI,cAAc,YAChB;aAEA;IAEJ;IAEA,qBACE,2BAAC,WAAK;QACJ,qBACE,2BAAC,WAAK;YAAC,OAAM;;8BACX,2BAAC,sBAAe;oBAAC,OAAO;wBAAE,UAAU;wBAAI,OAAO;oBAAU;;;;;;8BACzD,2BAAC;oBAAM,OAAO;oBAAG,OAAO;wBAAE,QAAQ;wBAAG,UAAU;oBAAG;8BAAG;;;;;;;;;;;;QAKzD,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ,cAAc,aAAa,SAAS;QAC5C,YAAW;QAEX,cAAc;kBAGd,cAAA,2BAAC,UAAI;YACH,WAAW;YACX,UAAU;YACV,OAAO;gBAAE,WAAW;YAAG;YACvB,OAAO;gBACL;oBACE,KAAK;oBACL,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,mBAAY;;;;;0CACb,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,wBACE,2BAAC;;0CACC,2BAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,2BAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;oCAAG;8CAAG;;;;;;;;;;;0CAKnD,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,cAAc;gCACd,cAAa;;kDAGb,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;4CACnC;gDAAE,KAAK;gDAAG,SAAS;4CAAW;4CAC9B;gDAAE,KAAK;gDAAI,SAAS;4CAAc;yCACnC;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;kDAKJ,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,KAAK;gDAAI,SAAS;4CAAc;yCACnC;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;kDAKJ,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;4CACnC;gDAAE,MAAM;gDAAS,SAAS;4CAAa;yCACxC;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;kDAKJ,2BAAC,UAAI,CAAC,IAAI;wCACR,qBACE,2BAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAK,UAAU;4CAAG;sDAAG;;;;;;wCAIlD,MAAK;wCACL,OAAO;4CACL;gDAAE,SAAS;gDAAiB,SAAS;4CAAa;yCACnD;kDAED,cAAA,2BAAC,WAAK;4CACJ,aAAY;4CACZ,OAAO;gDACL,cAAc;gDACd,UAAU;4CACZ;;;;;;;;;;;;;;;;;;;;;;;gBAMZ;gBACA;oBACE,KAAK;oBACL,qBACE,2BAAC,WAAK;wBAAC,OAAM;;0CACX,2BAAC,mBAAY;;;;;0CACb,2BAAC;0CAAK;;;;;;;;;;;;oBAGV,wBACE,2BAAC;;0CACC,2BAAC;gCAAI,OAAO;oCAAE,cAAc;oCAAI,WAAW;gCAAS;0CAClD,cAAA,2BAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;wCAAI,YAAY;oCAAI;8CAAG;;;;;;;;;;;0CAKpE,2BAAC,UAAI;gCACH,MAAM;gCACN,QAAO;gCACP,cAAc;gCACd,cAAa;0CAGb,cAAA,2BAAC,UAAI,CAAC,IAAI;oCACR,qBACE,2BAAC;wCAAK,OAAO;4CAAE,YAAY;4CAAK,UAAU;wCAAG;kDAAG;;;;;;oCAIlD,MAAK;oCACL,OAAO;wCACL;4CAAE,UAAU;4CAAM,SAAS;wCAAU;wCACrC;4CAAE,KAAK;4CAAG,SAAS;wCAAa;wCAChC;4CAAE,KAAK;4CAAI,SAAS;wCAAgB;qCACrC;oCACD,OAAO;wCAAE,cAAc;oCAAE;8CAEzB,cAAA,2BAAC,WAAK;wCACJ,aAAY;wCACZ,MAAK;wCACL,OAAO;4CACL,cAAc;4CACd,UAAU;4CACV,SAAS;4CACT,QAAQ;4CACR,YAAY;wCACd;wCACA,SAAS,CAAC;4CACR,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;wCACA,QAAQ,CAAC;4CACP,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;4CAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wCAC7B;;;;;;;;;;;;;;;;;;;;;;gBAMZ;aACD;;;;;;;;;;;AAIT;GArRM;;QAMmB,UAAI,CAAC;QACT,UAAI,CAAC;;;KAPpB;IAuRN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BC5Tf;;;eAAA;;;;IAAA,WAAe;IAAC,WAAW,CAAC,gBAAgB,CAAC;IAAC,SAAS,CAAC,cAAc,CAAC;IAAC,SAAS,CAAC,cAAc,CAAC;IAAC,SAAS,CAAC,cAAc,CAAC;IAAC,SAAS,CAAC,cAAc,CAAC;IAAC,kBAAkB,CAAC,uBAAuB,CAAC;IAAC,WAAW,CAAC,gBAAgB,CAAC;IAAC,aAAa,CAAC,kBAAkB,CAAC;IAAC,gBAAgB,CAAC,qBAAqB,CAAC;IAAC,eAAe,CAAC,oBAAoB,CAAC;IAAC,QAAQ,CAAC,aAAa,CAAC;IAAC,eAAe,CAAC,oBAAoB,CAAC;IAAC,QAAQ,CAAC,aAAa,CAAC;IAAC,UAAU,CAAC,eAAe,CAAC;IAAC,YAAY,CAAC,iBAAiB,CAAC;IAAC,YAAY,CAAC,iBAAiB,CAAC;IAAC,eAAe,CAAC,oBAAoB,CAAC;IAAC,gBAAgB,CAAC,qBAAqB,CAAC;AAAA;;;;;;;;4BC2K/kB;;;eAAA;;;;;;;8BAvKO;6BAMA;uEACW;kGAEC;;;;;;;;;AAEnB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;AAO3B;;;;;;;;;;;;;;;;;CAiBC,GACD,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,QAAQ,EACT;IACC,MAAM,+BACJ,2BAAC;QAAI,WAAW,yCAAM,CAAC,cAAc;kBACnC,cAAA,2BAAC,WAAK;YAAC,WAAU;YAAW,MAAM;YAAI,OAAO;gBAAE,OAAO;YAAO;;gBAE1D,SAAS,KAAK,kBACb,2BAAC;oBAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,KAAK,CAAC,CAAC;;sCAClD,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;sCAChC,cAAA,2BAAC,mBAAY;gCAAC,WAAW,yCAAM,CAAC,IAAI;gCAAE,OAAO;oCAAE,OAAO;gCAAU;;;;;;;;;;;sCAElE,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;;8CAChC,2BAAC;oCAAK,MAAK;oCAAY,WAAW,yCAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,2BAAC;oCAAK,WAAW,yCAAM,CAAC,KAAK;oCAAE,QAAQ;8CACpC,SAAS,KAAK;;;;;;;;;;;;;;;;;;gBAMtB,SAAS,SAAS,kBACjB,2BAAC;oBAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,KAAK,CAAC,CAAC;;sCAClD,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;sCAChC,cAAA,2BAAC,oBAAa;gCAAC,WAAW,yCAAM,CAAC,IAAI;gCAAE,OAAO;oCAAE,OAAO;gCAAU;;;;;;;;;;;sCAEnE,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;;8CAChC,2BAAC;oCAAK,MAAK;oCAAY,WAAW,yCAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,2BAAC;oCAAK,WAAW,yCAAM,CAAC,KAAK;oCAAE,QAAQ;8CACpC,SAAS,SAAS;;;;;;;;;;;;;;;;;;gBAMzB,CAAA,SAAS,KAAK,IAAI,SAAS,SAAS,AAAD,KAAM,SAAS,YAAY,kBAC9D,2BAAC,aAAO;oBAAC,WAAW,yCAAM,CAAC,OAAO;;;;;;gBAInC,SAAS,YAAY,kBACpB,2BAAC;oBAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC;;sCACrD,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;sCAChC,cAAA,2BAAC,uBAAgB;gCAAC,WAAW,yCAAM,CAAC,IAAI;gCAAE,OAAO;oCAAE,OAAO;gCAAU;;;;;;;;;;;sCAEtE,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;;8CAChC,2BAAC;oCAAK,MAAK;oCAAY,WAAW,yCAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,2BAAC;oCAAK,WAAW,yCAAM,CAAC,KAAK;8CAC1B,SAAS,YAAY;;;;;;;;;;;;;;;;;;gBAM7B,SAAS,aAAa,kBACrB,2BAAC;oBAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,SAAS,CAAC,CAAC;;sCACtD,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;sCAChC,cAAA,2BAAC,0BAAmB;gCAAC,WAAW,yCAAM,CAAC,IAAI;gCAAE,OAAO;oCAAE,OAAO;gCAAU;;;;;;;;;;;sCAEzE,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;;8CAChC,2BAAC;oCAAK,MAAK;oCAAY,WAAW,yCAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,2BAAC;oCAAK,WAAW,yCAAM,CAAC,KAAK;8CAC1B,SAAS,aAAa;;;;;;;;;;;;;;;;;;gBAM9B,SAAS,aAAa,kBACrB,2BAAC;oBAAI,WAAW,CAAC,EAAE,yCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,yCAAM,CAAC,IAAI,CAAC,CAAC;;sCACjD,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;sCAChC,cAAA,2BAAC,mBAAY;gCAAC,WAAW,yCAAM,CAAC,IAAI;gCAAE,OAAO;oCAAE,OAAO;gCAAU;;;;;;;;;;;sCAElE,2BAAC;4BAAI,WAAW,yCAAM,CAAC,WAAW;;8CAChC,2BAAC;oCAAK,MAAK;oCAAY,WAAW,yCAAM,CAAC,KAAK;8CAAE;;;;;;8CAGhD,2BAAC;oCAAK,WAAW,yCAAM,CAAC,KAAK;8CAC1B,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASrC,qBACE,2BAAC,aAAO;QACN,SAAS;QACT,qBACE,2BAAC;YAAI,WAAW,yCAAM,CAAC,YAAY;sBACjC,cAAA,2BAAC;gBAAK,MAAM;0BAAC;;;;;;;;;;;QAGjB,SAAS;YAAC;YAAS;SAAQ;QAC3B,WAAU;QACV,QAAQ;YACN,MAAM;gBACJ,SAAS;gBACT,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,WAAW;gBACX,QAAQ;YACV;QACF;QACA,OAAO;YACL,eAAe;QACjB;QACA,iBAAiB;QACjB,iBAAiB;QACjB,OAAO;QACP,QAAQ;kBAER,cAAA,2BAAC;YAAK,WAAW,yCAAM,CAAC,OAAO;sBAC5B;;;;;;;;;;;AAIT;KAjIM;IAmIN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Kf;;;;;;;;;CASC;;;;4BA0oBD;;;eAAA;;;;;;;wEAxoB2C;6BAqBpC;sCACkB;8BAclB;uEAEW;6BAGU;mCACM;kFASI;;;;;;;;;;AAEtC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;AASxB,MAAM,sBAA0D,CAAC,EAC/D,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,SAAS,EACV;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA2B,EAAE;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IAEtE,WAAW;IACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,WAAW;IACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAC;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IAEzC,WAAW;IACX,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;IAEjC,IAAA,gBAAS,EAAC;QACR,IAAI,WAAW,MAAM;YACnB;YACA;QACF;IACF,GAAG;QAAC;QAAS;KAAK;IAElB,WAAW;IACX,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iBAAW,CAAC,cAAc;YACjD,WAAW,SAAS,IAAI,IAAI,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,6BAAiB,CAAC,yBAAyB;YAClE,eAAe,YAAY,EAAE;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB,OAAO,UAAkB;QAClD,IAAI;YACF,MAAM,iBAAW,CAAC,YAAY,CAAC;YAC/B,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC;YACrC;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,YAAY,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IAAI,MAAM,CAAC,CAAA,QAAS;YACtF,MAAM,UAAgC;gBACpC,QAAQ;gBACR,SAAS,OAAO,OAAO;YACzB;YAEA,MAAM,iBAAW,CAAC,aAAa,CAAC;YAChC,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB,WAAW,WAAW;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,YAAY;YACZ,MAAM,iBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,oBAAoB;YACpB,KAAK,WAAW;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,sBAAsB,KAAK,IAAI,EAAE;YAC5C,aAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,IAAI;YACF,YAAY;YACZ,MAAM,iBAAW,CAAC,iBAAiB;YACnC,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,sBAAsB,CAAA,iBAAA,2BAAA,KAAM,SAAS,KAAI;IAE/C,IAAI,CAAC,MACH,OAAO;IAGT,UAAU;IACV,MAAM,gBAAkD;QACtD;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG;oBAGL,qBAAA;qCAFL,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,MAAK;4BAAQ,oBAAM,2BAAC,mBAAY;;;;;uCACrC,eAAA,OAAO,IAAI,cAAX,oCAAA,sBAAA,aAAa,MAAM,CAAC,gBAApB,0CAAA,oBAAwB,WAAW;;;;;;sCAEtC,2BAAC;;8CACC,2BAAC;oCAAI,OAAO;wCAAE,YAAY;oCAAI;8CAAI,OAAO,IAAI;;;;;;8CAC7C,2BAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAO;8CAAI,OAAO,KAAK;;;;;;;;;;;;;;;;;;;QAIlE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,SAAG;oBACF,MAAM,OAAO,SAAS,iBAAG,2BAAC,oBAAa;;;;+CAAM,2BAAC,mBAAY;;;;;oBAC1D,OAAO,OAAO,SAAS,GAAG,SAAS;8BAElC,OAAO,SAAS,GAAG,QAAQ;;;;;;QAGlC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAS,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;QACvC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAS,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC,sBAAsB;QACpE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,YAAY;8BAChC,WAAW,OAAO;;;;;;QAGzB;KACD;IAED,IAAI,qBACF,cAAc,IAAI,CAAC;QACjB,OAAO;QACP,KAAK;QACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;0BACH,CAAC,OAAO,SAAS,kBAChB,2BAAC,gBAAU;oBACT,OAAM;oBACN,WAAW,IAAM,mBAAmB,OAAO,EAAE,EAAE,OAAO,IAAI;oBAC1D,QAAO;oBACP,YAAW;8BAEX,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,MAAM;wBACN,MAAK;wBACL,oBAAM,2BAAC,qBAAc;;;;;kCACtB;;;;;;;;;;;;;;;;IAOX;IAGF,YAAY;IACZ,MAAM,oBAA0D;QAC9D;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC;;sCACC,2BAAC;4BAAI,OAAO;gCAAE,YAAY;4BAAI;sCAAI,OAAO,YAAY;;;;;;wBACpD,OAAO,WAAW,kBACjB,2BAAC;4BAAI,OAAO;gCAAE,UAAU;gCAAI,OAAO;4BAAO;sCAAI,OAAO,WAAW;;;;;;;;;;;;QAIxE;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,uBACP,2BAAC,yBAAyB;oBAAC,QAAQ;;;;;;QAEvC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAS,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;QACvC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,OAAS,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;QACvC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;KACD;IAED,IAAI,qBACF,kBAAkB,IAAI,CAAC;QACrB,OAAO;QACP,KAAK;QACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;0BACH,OAAO,cAAc,kBACpB,2BAAC,gBAAU;oBACT,OAAM;oBACN,WAAW,IAAM,uBAAuB,OAAO,EAAE;oBACjD,QAAO;oBACP,YAAW;8BAEX,cAAA,2BAAC,YAAM;wBACL,MAAK;wBACL,MAAM;wBACN,MAAK;wBACL,oBAAM,2BAAC,qBAAc;;;;;kCACtB;;;;;;;;;;;;;;;;IAOX;IAGF,OAAO;IACP,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,6BAAiB,CAAC,gBAAgB,CAAC;YACzC,aAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,qBACE;;0BACE,2BAAC,WAAK;gBACJ,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,mBAAY;;;;;sCACb,2BAAC;;gCAAK;gCAAQ,KAAK,IAAI;;;;;;;;;;;;;gBAG3B,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ;gBACR,cAAc;0BAEd,cAAA,2BAAC,UAAI;oBAAC,kBAAiB;;sCACrB,2BAAC;4BAAQ,KAAI;sCACX,cAAA,2BAAC,UAAI;gCACH,OAAM;gCACN,OACE,qCACE,2BAAC,YAAM;oCACL,MAAK;oCACL,oBAAM,2BAAC,mBAAY;;;;;oCACnB,SAAS,IAAM,sBAAsB;8CACtC;;;;;;0CAML,cAAA,2BAAC,uBAAQ;oCACP,SAAS;oCACT,YAAY;oCACZ,QAAO;oCACP,SAAS;oCACT,QAAQ;oCACR,YAAY;wCACV,iBAAiB;wCACjB,iBAAiB;wCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;wCACtC,UAAU;oCACZ;oCACA,SAAS;wCACP,QAAQ,IAAM;wCACd,SAAS;wCACT,SAAS;oCACX;oCACA,MAAK;;;;;;;;;;;2BAhCa;;;;;sCAqCxB,2BAAC;4BAAQ,KAAI;sCACX,cAAA,2BAAC,UAAI;gCAAC,OAAM;0CACV,cAAA,2BAAC,uBAAQ;oCACP,SAAS;oCACT,YAAY;oCACZ,QAAO;oCACP,SAAS;oCACT,QAAQ;oCACR,YAAY;wCACV,iBAAiB;wCACjB,iBAAiB;wCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;wCACtC,UAAU;oCACZ;oCACA,SAAS;wCACP,QAAQ,IAAM;wCACd,SAAS;wCACT,SAAS;oCACX;oCACA,MAAK;;;;;;;;;;;2BAnBa;;;;;wBAwBvB,qCACC,2BAAC;4BAAQ,KAAI;sCACX,cAAA,2BAAC,UAAI;gCAAC,OAAM;0CACV,cAAA,2BAAC,WAAK;oCAAC,WAAU;oCAAW,OAAO;wCAAE,OAAO;oCAAO;oCAAG,MAAK;;sDACzD,2BAAC;;8DACC,2BAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,2BAAC,WAAK;8DACJ,cAAA,2BAAC,YAAM;wDACL,oBAAM,2BAAC,mBAAY;;;;;wDACnB,SAAS;4DACP,KAAK,cAAc,CAAC;gEAClB,MAAM,KAAK,IAAI;gEACf,aAAa,KAAK,WAAW;4DAC/B;4DACA,oBAAoB;wDACtB;kEACD;;;;;;;;;;;;;;;;;sDAML,2BAAC;;8DACC,2BAAC;oDAAM,OAAO;oDAAG,MAAK;8DAAS;;;;;;8DAC/B,2BAAC,WAAK;oDACJ,SAAQ;oDACR,aAAY;oDACZ,MAAK;oDACL,QAAQ;oDACR,sBACE,2BAAC,YAAM;wDACL,MAAM;wDACN,oBAAM,2BAAC,qBAAc;;;;;wDACrB,SAAS,IAAM,sBAAsB;kEACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAjCW;;;;;;;;;;;;;;;;0BA+C9B,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,WAAW,WAAW;gBACxB;gBACA,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;kDAAS;;;;;;kDAGzC,2BAAC,YAAM;wCAAC,SAAS;4CACf,sBAAsB;4CACtB,WAAW,WAAW;wCACxB;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,oBAAoB;oBACpB,KAAK,WAAW;gBAClB;gBACA,QAAQ;0BAER,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,SAAS;kDAAU;;;;;;kDAG5D,2BAAC,YAAM;wCAAC,SAAS;4CACf,oBAAoB;4CACpB,KAAK,WAAW;wCAClB;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,qBAAqB;gBACvB;gBACA,QAAQ;kCACN,2BAAC,YAAM;wBAEL,SAAS;4BACP,sBAAsB;4BACtB,qBAAqB;wBACvB;kCACD;uBALK;;;;;kCAQN,2BAAC,YAAM;wBAEL,MAAK;wBACL,MAAM;wBACN,SAAS;wBACT,UAAU,sBAAsB,KAAK,IAAI;wBACzC,SAAS;kCACV;uBANK;;;;;iBASP;;kCAED,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC;;0CACC,2BAAC;;oCAAK;kDAAQ,2BAAC;wCAAK,IAAI;kDAAE,KAAK,IAAI;;;;;;oCAAQ;;;;;;;0CAC3C,2BAAC,WAAK;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,aAAa,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC;gCAC/B,OAAO;oCAAE,WAAW;gCAAE;;;;;;;;;;;;;;;;;;;;AAMlC;GAtkBM;;QAeW,UAAI,CAAC;QASC,UAAI,CAAC;;;KAxBtB;IAwkBN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BCjdf;;;eAAA;;;;;;;4BAlMyB;6BACM;sCACP;yDACN;+DACU;gEACH;kEACE;gEACF;gEACA;;;;;;;;;;AA8BzB,MAAM,qBAA+B;;IAOnC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,aAAQ,EAAC;IAU7B,IAAI,SACF,OACE,2BAAC;QACC,OAAO;YACL,WAAW;YACX,YAAY;YACZ,SAAS;YACT,gBAAgB;YAChB,YAAY;QACd;;YAEA,2BAAC,UAAI;gBAAC,MAAK;;;;;;YACX,2BAAC;gBAAI,OAAO;oBAAE,YAAY;gBAAG;0BAAG;;;;;;;;;;;;IAgBtC,OACE;;YAEE,2BAAC;gBACC,OAAO;oBACL,WAAW;oBACX,YAAY;oBACZ,SAAS;gBACX;0BAWA,2BAAC,sBAAO;oBACN,OAAO;wBACL,OAAO;wBACP,WAAW;wBACX,cAAc;wBACd,WAAW;oBACb;oBACA,WAAW;wBACT,SAAS;oBACX;8BAWA,2BAAC,SAAG;wBAAC,QAAQ;4BAAC;4BAAI;yBAAG;wBAAE,OAAO;4BAAE,QAAQ;wBAAE;;4BAaxC,2BAAC,SAAG;gCACF,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,KAAK;;oCAGL,2BAAC,qBAAY;;;;;oCAGb,2BAAC,qBAAY;;;;;oCAGb,2BAAC,qBAAY;;;;;;;;;;;4BAWf,2BAAC,SAAG;gCACF,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,IAAI;gCACJ,KAAK;0CAEL,2BAAC,uBAAc;;;;;;;;;;;;;;;;;;;;;;;;;;YAgBvB,2BAAC,oBAAe;;;;;;;AAOtB;GA1JM;;QAOgB,aAAQ;;;KAPxB;IA4JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AClMf;;CAEC;;;;4BAUY;;;eAAA;;;;;gCAFc;;;;;;;;;AAEpB,MAAM;IACX;;GAEC,GACD,aAAa,eAAwC;QACnD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,OAA0B,EAAyB;QACzE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAe,UAAU;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WACX,EAAU,EACV,OAA0B,EACH;QACvB,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,OAAO,EAAE,GAAG,CAAC,EACd;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,aAAa,WAAW,EAAU,EAAiB;QACjD,MAAM,mBAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC;IACxC;IAEA;;GAEC,GACD,aAAa,eAA2C;QACtD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAoB;QACzD,OAAO,SAAS,IAAI;IACtB;AACF;;;;;;;;;;;;;;;;;;;;;;;;;ACzDA;;;CAGC,GAED,eAAe;;;;;;;;;;;;IAmDF,6BAA6B;eAA7B;;IAaA,uBAAuB;eAAvB;;IAxDA,2BAA2B;eAA3B;;IAkCA,mBAAmB;eAAnB;;IAjBA,mBAAmB;eAAnB;;;;;;;;;;;;;AAxBb,MAAM,qBAAqB;AAOpB,MAAM,8BAA8B,CAAC;IAC1C,IAAI;QACF,MAAM,UAAU,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACtE,IAAI,SACF,OAAO,IAAI,IAAI,KAAK,KAAK,CAAC;IAE9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;IACA,OAAO,IAAI;AACb;AAOO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,IAAI;QACF,MAAM,UAAU,4BAA4B;QAC5C,QAAQ,GAAG,CAAC;QACZ,aAAa,OAAO,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,SAAS,CAAC;eAAI;SAAQ;QACnF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,KAAK,EAAE,OAAO,CAAC;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAQO,MAAM,sBAAsB,CAAC,QAAgB;IAClD,MAAM,UAAU,4BAA4B;IAC5C,OAAO,QAAQ,GAAG,CAAC;AACrB;AAMO,MAAM,gCAAgC,CAAC;IAC5C,IAAI;QACF,aAAa,UAAU,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;QACzD,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;IAC/B;AACF;AAMO,MAAM,0BAA0B;IACrC,MAAM,OAAiB,EAAE;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,IAAI,OAAO,IAAI,UAAU,CAAC,qBACxB,KAAK,IAAI,CAAC;IAEd;IACA,OAAO;AACT"}