import {
  QuestionCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';

import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse } from '@/types/api';
import UnifiedSettingsModal from './UnifiedSettingsModal';
import UserInfoPopover from './UserInfoPopover';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的基本个人信息，采用简洁的卡片设计。
 * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名（支持气泡卡片显示详细信息）
 * 3. 显示最后登录时间和登录团队
 * 4. 提供设置入口
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // Modal状态管理
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <>
      <ProCard
        style={{
          marginBottom: 16,
          borderRadius: 8,
          border: '1px solid #d9d9d9',
          position: 'relative',
        }}
        bodyStyle={{
          padding: '20px',
        }}
      >
        {/* 右上角图标区域 */}
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          zIndex: 10,
        }}>
          <Space size={16}>
            <UserInfoPopover userInfo={userInfo}>
              <QuestionCircleOutlined
                style={{
                  fontSize: 18,
                  color: '#8c8c8c',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  padding: '4px',
                  borderRadius: '50%',
                  background: 'transparent',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#1890ff';
                  e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                  e.currentTarget.style.transform = 'scale(1.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#8c8c8c';
                  e.currentTarget.style.background = 'transparent';
                  e.currentTarget.style.transform = 'scale(1)';
                }}
              />
            </UserInfoPopover>
            <SettingOutlined
              style={{
                fontSize: 18,
                color: '#8c8c8c',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                padding: '4px',
                borderRadius: '50%',
                background: 'transparent',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#1890ff';
                e.currentTarget.style.background = 'rgba(24, 144, 255, 0.08)';
                e.currentTarget.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#8c8c8c';
                e.currentTarget.style.background = 'transparent';
                e.currentTarget.style.transform = 'scale(1)';
              }}
              onClick={() => setSettingsModalVisible(true)}
            />
          </Space>
        </div>

        {userInfoError ? (
          <Alert
            message="个人信息加载失败"
            description={userInfoError}
            type="error"
            showIcon
            style={{
              borderRadius: 12,
              border: 'none',
            }}
          />
        ) : (
          <Spin spinning={userInfoLoading}>
            {/* 问候区域 */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '16px 0',
              marginTop: '20px', // 为右上角图标留出空间
            }}>
              {/* 问候语 */}
              <Typography.Title
                level={3}
                style={{
                  margin: 0,
                  fontSize: 20,
                  color: '#262626',
                }}
              >
                您好，{userInfo.name || '加载中...'}
              </Typography.Title>
            </div>
          </Spin>
        )}
      </ProCard>

      {/* 统一设置Modal */}
      <UnifiedSettingsModal
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        userInfo={userInfo}
        onSuccess={() => {
          // 可以在这里刷新用户信息
          console.log('设置操作成功');
        }}
      />
    </>
  );
};

export default PersonalInfo;
