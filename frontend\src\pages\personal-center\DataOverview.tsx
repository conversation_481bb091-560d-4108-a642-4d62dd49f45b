import {
  BarChartOutlined,
  CarOutlined,
  UsergroupAddOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Card,
  Col,
  Row,
  Spin,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserPersonalStatsResponse } from '@/types/api';

/**
 * 数据概览卡片组件
 *
 * 显示用户的个人统计数据，采用单行四列的水平布局。
 * 包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计
 * 2. 显示人员数量统计
 * 3. 显示预警数量统计
 * 4. 显示告警数量统计
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 布局特点：
 * - 单行四列水平排列
 * - 每个统计项独立的卡片设计
 * - 响应式布局适配不同屏幕
 */
const DataOverview: React.FC = () => {
  // 定义内联样式对象
  const cardStyles = {
    base: {
      borderRadius: '8px',
      border: '1px solid #d9d9d9',
      height: '120px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent', // 移除背景色
    },
    vehicle: {
      // backgroundColor: '#1890ff', // 移除背景色
      // color: 'white', // 移除白色文字
    },
    personnel: {
      // backgroundColor: '#52c41a', // 移除背景色
      // color: 'white', // 移除白色文字
    },
    warning: {
      // backgroundColor: '#faad14', // 移除背景色
      // color: 'white', // 移除白色文字
    },
    alert: {
      // backgroundColor: '#f5222d', // 移除背景色
      // color: 'white', // 移除白色文字
    },
  };
  /**
   * 个人统计数据状态管理
   */
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });

  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStatsData();
  }, []);

  return (
    <ProCard
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />
          <span>数据概览</span>
        </div>
      }
      style={{
        marginBottom: 16,
        borderRadius: 8,
        border: '1px solid #d9d9d9',
      }}
      headStyle={{
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: 12,
      }}
      bodyStyle={{
        padding: '20px',
      }}
    >
      {statsError ? (
        <Alert
          message="数据概览加载失败"
          description={statsError}
          type="error"
          showIcon
          style={{
            borderRadius: 8,
          }}
        />
      ) : (
        <Spin spinning={statsLoading}>
          {/* 单行四列布局 */}
          <Row gutter={[16, 16]}>
            {/* 车辆统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <Card
                style={{
                  ...cardStyles.base,
                  ...cardStyles.vehicle,
                }}
                styles={{
                  body: {
                    padding: '20px 16px',
                    textAlign: 'center',
                  },
                }}

              >
                <div style={{ marginBottom: 12 }}>
                  <CarOutlined
                    style={{
                      fontSize: 24,
                      color: '#1890ff',
                      marginBottom: 8,
                    }}
                  />
                </div>
                <div
                  style={{
                    fontSize: 32,
                    fontWeight: 700,
                    color: '#1890ff',
                    lineHeight: 1,
                    marginBottom: 8,
                  }}
                >
                  {personalStats.vehicles}
                </div>
                <div
                  style={{
                    fontSize: 14,
                    color: '#1890ff',
                    fontWeight: 600,
                    opacity: 0.9, // 提高透明度以确保可读性
                  }}
                >
                  车辆
                </div>
              </Card>
            </Col>

            {/* 人员统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <Card
                style={{
                  ...cardStyles.base,
                  ...cardStyles.personnel,
                }}
                styles={{
                  body: {
                    padding: '20px 16px',
                    textAlign: 'center',
                  },
                }}

              >
                <div style={{ marginBottom: 12 }}>
                  <UsergroupAddOutlined
                    style={{
                      fontSize: 24,
                      color: '#52c41a',
                      marginBottom: 8,
                    }}
                  />
                </div>
                <div
                  style={{
                    fontSize: 32,
                    fontWeight: 700,
                    color: '#52c41a',
                    lineHeight: 1,
                    marginBottom: 8,
                  }}
                >
                  {personalStats.personnel}
                </div>
                <div
                  style={{
                    fontSize: 14,
                    color: '#52c41a',
                    fontWeight: 600,
                    opacity: 0.9, // 提高透明度以确保可读性
                  }}
                >
                  人员
                </div>
              </Card>
            </Col>

            {/* 预警统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <Card
                style={{
                  ...cardStyles.base,
                  ...cardStyles.warning,
                }}
                styles={{
                  body: {
                    padding: '20px 16px',
                    textAlign: 'center',
                  },
                }}

              >
                <div style={{ marginBottom: 12 }}>
                  <ExclamationCircleOutlined
                    style={{
                      fontSize: 24,
                      color: '#faad14',
                      marginBottom: 8,
                    }}
                  />
                </div>
                <div
                  style={{
                    fontSize: 32,
                    fontWeight: 700,
                    color: '#faad14',
                    lineHeight: 1,
                    marginBottom: 8,
                  }}
                >
                  {personalStats.warnings}
                </div>
                <div
                  style={{
                    fontSize: 14,
                    color: '#faad14',
                    fontWeight: 600,
                    opacity: 0.9, // 提高透明度以确保可读性
                  }}
                >
                  预警
                </div>
              </Card>
            </Col>

            {/* 告警统计 */}
            <Col xs={12} sm={6} md={6} lg={6} xl={6}>
              <Card
                style={{
                  ...cardStyles.base,
                  ...cardStyles.alert,
                }}
                styles={{
                  body: {
                    padding: '20px 16px',
                    textAlign: 'center',
                  },
                }}

              >
                <div style={{ marginBottom: 12 }}>
                  <AlertOutlined
                    style={{
                      fontSize: 24,
                      color: '#ff4d4f',
                      marginBottom: 8,
                    }}
                  />
                </div>
                <div
                  style={{
                    fontSize: 32,
                    fontWeight: 700,
                    color: '#ff4d4f',
                    lineHeight: 1,
                    marginBottom: 8,
                  }}
                >
                  {personalStats.alerts}
                </div>
                <div
                  style={{
                    fontSize: 14,
                    color: '#ff4d4f',
                    fontWeight: 600,
                    opacity: 0.9, // 提高透明度以确保可读性
                  }}
                >
                  告警
                </div>
              </Card>
            </Col>
          </Row>
        </Spin>
      )}
    </ProCard>
  );
};

export default DataOverview;
